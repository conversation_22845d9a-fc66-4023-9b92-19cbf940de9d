package request

type MoreCreatorMixApiGoodsParseRequest struct {
	Cookie        string `json:"cookie"`
	Proxy         string `json:"proxy,omitempty"`
	PromotionLink string `json:"promotion_link"` // 商品链接

}

type MoreCreatorMixApiUpdatePromotionRequest struct {
	Cookie       string `json:"cookie"`
	Proxy        string `json:"proxy,omitempty"`
	PromotionId  string `json:"promotion_id"`
	ElasticTitle string `json:"elastic_title"`
	ElasticImg   string `json:"elastic_img"`
	ProductId    string `json:"product_id"`
}

type CreateVod struct {
	Description      string `json:"description"`
	UploadPosterPath string `json:"upload_poster" comment:"视频封面"`
	VisibilityType   string `json:"visibility_type" comment:"可见状态(0,：全部可见，1：自己可见，2：朋友可见)	"`
	VideoPath        string `json:"video,omitempty"`
	VideoVid         string `json:"video_vid,omitempty"`
	Download         string `json:"download,omitempty"`
	Challenges       string `json:"challenges,omitempty"`
	Timing           string `json:"timing,omitempty"`
	PoiName          string `json:"poi_name,omitempty"`
	PoiId            string `json:"poi_id,omitempty"`
	ProductUrl       string `json:"product_url,omitempty"`
	ShopDraftId      string `json:"shop_draft_id"`
}

type MoreCreatorMixApiCreateVodRequest struct {
	Cookie           string `json:"cookie"`
	Proxy            string `json:"proxy,omitempty"`
	Description      string `json:"description"`
	UploadPosterPath string `json:"upload_poster" comment:"视频封面"`
	VisibilityType   string `json:"visibility_type" comment:"可见状态(0,：全部可见，1：自己可见，2：朋友可见)	"`
	VideoPath        string `json:"video,omitempty"`
	VideoVid         string `json:"video_vid,omitempty"`
	Download         string `json:"download,omitempty"`
	Challenges       string `json:"challenges,omitempty"`
	Timing           string `json:"timing,omitempty"`
	PoiName          string `json:"poi_name,omitempty"`
	PoiId            string `json:"poi_id,omitempty"`
	ProductUrl       string `json:"product_url,omitempty"`
	ShopDraftId      string `json:"shop_draft_id"`
}

type MoreCreatorMixApiCreatePromotionRequest struct {
	WebCookie      string `json:"web_cookie"`
	Cookie         string `json:"cookie"`
	Proxy          string `json:"proxy,omitempty"`
	PromotionLink  string `json:"promotion_link"`  // 商品链接
	PromotionTitle string `json:"promotion_title"` // 商品标题

	Description      string `json:"description"`
	UploadPosterPath string `json:"upload_poster" comment:"视频封面"`
	VisibilityType   string `json:"visibility_type" comment:"可见状态(0,：全部可见，1：自己可见，2：朋友可见)	"`
	VideoPath        string `json:"video,omitempty"`
	VideoVid         string `json:"video_vid,omitempty"`
	Download         string `json:"download,omitempty"`
	Challenges       string `json:"challenges,omitempty"`
	Timing           string `json:"timing,omitempty"`
	PoiName          string `json:"poi_name,omitempty"`
	PoiId            string `json:"poi_id,omitempty"`
	ProductUrl       string `json:"product_url,omitempty"`
}
