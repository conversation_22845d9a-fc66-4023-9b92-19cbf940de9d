package douyin

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/logic"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/douyin"
	"github.com/flipped-aurora/gin-vue-admin/server/model/douyin/request"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
)

type DyAwemeApi struct{}

// 获取作品列表
func (l *DyAwemeApi) GetAwemeList(c *gin.Context) {
	var req request.AwemeListSearch
	if err := c.ShouldBindQuery(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 获取当前用户ID
	currentUserID := utils.GetUserID(c)
	// 获取当前用户及其下级用户的ID列表
	userIds, err := userService.GetSubordinateUserIds(currentUserID)
	if err != nil {
		response.FailWithMessage("获取用户权限失败: "+err.Error(), c)
		return
	}
	// 添加当前用户ID
	userIds = append(userIds, currentUserID)
	req.SysUserIds = userIds

	if req.DyUserIds != "" {
		dyUserIds := strings.Split(req.DyUserIds, ",")
		dyUserIdsUint := make([]uint, 0, len(dyUserIds))
		for _, dyUserId := range dyUserIds {
			dyUserIdUint, err := strconv.ParseUint(dyUserId, 10, 64)
			if err != nil {
				response.FailWithMessage("用户选取有误: "+err.Error(), c)
				return
			}
			dyUserIdsUint = append(dyUserIdsUint, uint(dyUserIdUint))
		}

		var dyUsers []douyin.DyUser
		err = global.GVA_DB.Model(&douyin.DyUser{}).Where("id in (?)", dyUserIdsUint).Find(&dyUsers).Error
		if err != nil {
			response.FailWithMessage("查询抖音用户失败: "+err.Error(), c)
			return
		}
		for _, dyUser := range dyUsers {
			req.UniqueIds = append(req.UniqueIds, dyUser.UniqueId)
		}
	}

	list, total, err := dyAwemeService.GetAwemeList(req)
	if err != nil {
		response.FailWithMessage("查询失败: "+err.Error(), c)
		return
	}
	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, "获取成功", c)
}

// 同步管理作品
func (l *DyAwemeApi) ManageAweme(c *gin.Context) {
	var req request.ManageAwemeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage("参数错误："+err.Error(), c)
		return
	}

	dyUserIds := strings.Split(req.DyUserIds, ",")
	dyUserIdsUint := make([]uint, 0, len(dyUserIds))
	for _, dyUserId := range dyUserIds {
		dyUserIdUint, err := strconv.ParseUint(dyUserId, 10, 64)
		if err != nil {
			response.FailWithMessage("用户选取有误: "+err.Error(), c)
			return
		}
		dyUserIdsUint = append(dyUserIdsUint, uint(dyUserIdUint))
	}

	var users []*douyin.DyUser
	err := global.GVA_DB.Model(&douyin.DyUser{}).Where("id in (?)", dyUserIdsUint).Find(&users).Error
	if err != nil {
		response.FailWithMessage("查询抖音用户失败: "+err.Error(), c)
		return
	}

	errUsers := []string{}
	awemeLogic := logic.DyAwemeLogic{}
	var wg sync.WaitGroup
	var mutex sync.Mutex

	for _, dyUser := range users {
		wg.Add(1)
		go func(user *douyin.DyUser) {
			defer wg.Done()

			// 设置一个5分钟的redis锁，防止重复执行
			ctx := context.Background()
			lockKey := fmt.Sprintf("manage_aweme_request:%s", user.UniqueId)
			// 锁存在时，跳过
			if global.GVA_REDIS.Exists(ctx, lockKey).Val() > 0 {
				errUsers = append(errUsers, fmt.Sprintf("%s:同步过于频繁，五分钟内只能同步一次", user.Nickname))
				return
			}

			// 设置锁，5分钟后自动释放
			_, err := global.GVA_REDIS.SetNX(ctx, lockKey, 1, time.Duration(5*60)*time.Second).Result()
			if err != nil {
				errUsers = append(errUsers, fmt.Sprintf("%s:设置锁失败", user.Nickname))
				return
			}

			err = awemeLogic.ManageAwemeList(user, 1)
			if err != nil {
				mutex.Lock()
				errUsers = append(errUsers, fmt.Sprintf("%s", err.Error()))
				mutex.Unlock()
			}
		}(dyUser)
	}
	wg.Wait()
	if len(errUsers) > 0 {
		response.FailWithMessage(fmt.Sprintf("%v", errUsers), c)
		return
	}

	response.Ok(c)
}

// 修改作品权限
func (l *DyAwemeApi) UpdateAweme(c *gin.Context) {
	var req struct {
		PrivateStatus int `json:"private_status"`
		DyAwemeId     int `json:"dyAwemeId"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage("参数错误："+err.Error(), c)
		return
	}

	// 获取作品信息
	var aweme douyin.DyAweme
	if err := global.GVA_DB.Where("id =?", req.DyAwemeId).First(&aweme).Error; err != nil {
		response.FailWithMessage("获取作品信息失败："+err.Error(), c)
		return
	}

	// 如果sysDsipose为4,表示已删除，不能修改权限：
	// 修改已删除的作品权限，抖音并无报错，因此手动处理
	if aweme.SysDispose == 4 {
		response.FailWithMessage("该作品已删除", c)
		return
	}

	// 根据aweme.UniqueId获取dyUser
	dyUser, err := dyUserService.GetUserByUniqueId(aweme.UniqueId)
	if err != nil {
		response.FailWithMessage("获取抖音用户信息失败："+err.Error(), c)
		return
	}

	awemeLogic := logic.DyAwemeLogic{}
	err = awemeLogic.EditAwemePermission(dyUser, &aweme, req.PrivateStatus, 2)
	if err != nil {
		response.FailWithMessage("远程修改作品失败:"+err.Error(), c)
		return
	}

	// 修改数据库的aweme信息
	err = dyAwemeService.UpdateAweme(&aweme)
	if err != nil {
		response.FailWithMessage("编辑失败:"+err.Error(), c)
		return
	}

	response.Ok(c)
}

// 删除作品
func (l *DyAwemeApi) DeleteAweme(c *gin.Context) {
	var req struct {
		DyAwemeId int `json:"dyAwemeId"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage("参数错误："+err.Error(), c)
		return
	}

	// 获取作品信息
	var aweme douyin.DyAweme
	if err := global.GVA_DB.Where("id =?", req.DyAwemeId).First(&aweme).Error; err != nil {
		response.FailWithMessage("获取作品信息失败："+err.Error(), c)
		return
	}

	// 根据aweme.UniqueId获取dyUser
	dyUser, err := dyUserService.GetUserByUniqueId(aweme.UniqueId)
	if err != nil {
		response.FailWithMessage("获取抖音用户信息失败："+err.Error(), c)
		return
	}

	awemeLogic := logic.DyAwemeLogic{}
	err = awemeLogic.DeleteAweme(dyUser, &aweme, 2)
	if err != nil {
		response.FailWithMessage("远程删除作品失败:"+err.Error(), c)
		return
	}

	err = dyAwemeService.UpdateAweme(&aweme)
	if err != nil {
		response.FailWithMessage("删除失败:"+err.Error(), c)
		return
	}

	response.Ok(c)
}

// 批量处理作品
func (l *DyAwemeApi) BatchDisposeAweme(c *gin.Context) {
	var req request.BatchDisposeAweme
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage("参数错误："+err.Error(), c)
		return
	}

	awemeIds := strings.Split(req.DyAwemeIds, ",")
	awemeIdsUint := make([]uint, 0, len(awemeIds))
	for _, awemeId := range awemeIds {
		dyAwemeIdUint, err := strconv.ParseUint(awemeId, 10, 64)
		if err != nil {
			response.FailWithMessage("作品选取有误: "+err.Error(), c)
			return
		}
		awemeIdsUint = append(awemeIdsUint, uint(dyAwemeIdUint))
	}

	var awemes []*douyin.DyAweme
	err := global.GVA_DB.Model(&douyin.DyAweme{}).Where("id in (?)", awemeIdsUint).Find(&awemes).Error
	if err != nil {
		response.FailWithMessage("查询作品失败: "+err.Error(), c)
		return
	}

	var uniqueIds []string
	uniqueIdMap := make(map[string]struct{})
	for _, aweme := range awemes {
		if _, existed := uniqueIdMap[aweme.UniqueId]; !existed {
			uniqueIdMap[aweme.UniqueId] = struct{}{}
			uniqueIds = append(uniqueIds, aweme.UniqueId)
		}
	}

	updateData := map[string]any{
		"sys_dispose":        req.Action,
		"sys_dispose_method": 2,
		"sys_dispose_time":   0,
	}
	err = global.GVA_DB.Model(&douyin.DyAweme{}).Where("id in (?)", awemeIdsUint).Updates(updateData).Error
	if err != nil {
		response.FailWithMessage("批量操作失败: "+err.Error(), c)
		return
	}

	// 将成员添加到集合中
	if len(uniqueIds) > 0 {
		// 将 []string 类型的 uniqueIds 转换为 []interface{} 类型
		args := make([]interface{}, len(uniqueIds))
		for i, v := range uniqueIds {
			args[i] = v
		}
		err = global.GVA_REDIS.SAdd(context.Background(), douyin.DisposeAwemeUserSetKey, args...).Err()
		if err != nil {
			response.FailWithMessage("添加到集合失败: "+err.Error(), c)
			return
		}
	}

	response.Ok(c)
}
