package douyin

import (
	"encoding/json"
	"fmt"
	"strings"

	common "github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/douyin"
	"github.com/flipped-aurora/gin-vue-admin/server/model/douyin/request"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
)

type DyUserApi struct{}

// AddUser 添加抖音用户（账号授权）
// @Tags      DyUser
// @Summary   添加抖音用户
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      request.AddUserRequest  true  "抖音用户数据"
// @Success   200   {object}  response.Response{data=douyin.DyUser,msg=string}  "创建成功"
// @Router    /douyin/user/add [post]
func (e *DyUserApi) AddUser(c *gin.Context) {
	var req request.AddUserRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 获取当前登录用户
	userID := utils.GetUserID(c)

	// 获取用户信息
	// 尝试从IP池中获取可用IP
	var bindIP string
	availableIPs, err := dyIPService.GetAvailableIP()
	if err == nil && len(availableIPs) > 0 {
		// 如果有可用IP，选择第一个（已按用户数排序）
		bindIP = availableIPs[0]
	}
	// 如果没有可用IP，授权失败，因为没IP无法从火苗获取到用户信息
	if bindIP == "" {
		response.FailWithMessage("没有可用IP，授权失败", c)
		return
	}

	// 调用火苗API获取用户信息
	userInfo, err := awemeService.GetUserInfo(req.ImToken, bindIP)
	if err != nil {
		response.FailWithMessage("获取用户信息失败: "+err.Error(), c)
		return
	}

	// 创建用户
	dyUser := &douyin.DyUser{
		ImToken:    req.ImToken,
		CategoryId: req.CategoryId,
		SysUserId:  int64(userID),
		Did:        req.Did,
		BindIP:     bindIP,
	}

	// 复制用户信息
	avatarBytes, _ := json.Marshal(userInfo.User.Avatar168x168)
	avatar := string(avatarBytes)
	dyUser.UID = userInfo.User.UID
	dyUser.Nickname = userInfo.User.Nickname
	dyUser.Avatar = avatar
	dyUser.UniqueId = userInfo.User.UniqueId
	dyUser.ShortId = userInfo.User.ShortId
	dyUser.SecUid = userInfo.User.SecUid
	dyUser.FollowerCount = userInfo.User.FollowerCount
	dyUser.FollowingCount = userInfo.User.FollowingCount
	dyUser.AwemeCount = userInfo.User.AwemeCount
	dyUser.TotalFavorited = userInfo.User.TotalFavorited
	dyUser.AccountRegion = userInfo.User.AccountRegion
	dyUser.Province = userInfo.User.Province
	dyUser.City = userInfo.User.City
	dyUser.CollegeName = userInfo.User.CollegeName
	dyUser.BindPhone = userInfo.User.BindPhone
	dyUser.Birthday = userInfo.User.Birthday
	dyUser.Gender = userInfo.User.Gender
	dyUser.Signature = userInfo.User.Signature

	err = dyUserService.AddUser(dyUser)
	if err != nil {
		response.FailWithMessage("创建用户失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(dyUser, "创建成功", c)
}

// GetUserList
// @Tags      DyUser
// @Summary   获取抖音用户列表
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  query     request.DyUserSearch                                        true  "页码, 每页大小, 筛选条件"
// @Success   200   {object}  response.Response{data=response.PageResult,msg=string}      "获取成功"
// @Router    /douyin/user/list [get]
func (e *DyUserApi) GetUserList(c *gin.Context) {
	var pageInfo request.DyUserSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 获取当前用户ID
	currentUserID := utils.GetUserID(c)

	// 获取当前用户及其下级用户的ID列表
	userIds, err := userService.GetSubordinateUserIds(currentUserID)
	if err != nil {
		response.FailWithMessage("获取用户权限失败: "+err.Error(), c)
		return
	}
	// 添加当前用户ID
	userIds = append(userIds, currentUserID)

	// 设置当前用户ID到搜索参数中，用于获取排序信息
	pageInfo.CurrentUserID = currentUserID

	list, total, err := dyUserService.GetUserList(pageInfo, userIds)
	if err != nil {
		response.FailWithMessage("获取失败: "+err.Error(), c)
		return
	}
	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// DeleteUser
// @Tags      DyUser
// @Summary   删除抖音用户
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      common.GetById                true  "用户ID"
// @Success   200   {object}  response.Response{msg=string}  "删除成功"
// @Router    /douyin/user/delete [delete]
func (e *DyUserApi) DeleteUser(c *gin.Context) {
	var req common.GetById
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := dyUserService.DeleteUser(uint(req.ID)); err != nil {
		response.FailWithMessage("删除失败: "+err.Error(), c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

// ToggleProductEnabled
// @Tags      DyUser
// @Summary   切换选品状态
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      request.ToggleProductEnabledRequest  true  "用户ID和选品状态"
// @Success   200   {object}  response.Response{msg=string}        "操作成功"
// @Router    /douyin/user/toggle-product [post]
func (e *DyUserApi) ToggleProductEnabled(c *gin.Context) {
	var req request.ToggleProductEnabledRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := dyUserService.ToggleProductEnabled(uint(req.ID), req.Enabled); err != nil {
		response.FailWithMessage("操作失败: "+err.Error(), c)
		return
	}

	statusText := "关闭"
	if req.Enabled {
		statusText = "开启"
	}
	response.OkWithMessage("选品已"+statusText, c)
}

// UpdateUserIP 更新用户绑定IP
// @Tags      DyUser
// @Summary   更新用户绑定IP
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      request.UpdateUserIPRequest  true  "用户ID和新IP"
// @Success   200   {object}  response.Response{msg=string}  "更新成功"
// @Router    /douyin/user/update-ip [post]
func (e *DyUserApi) UpdateUserIP(c *gin.Context) {
	var req request.UpdateUserIPRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage("参数错误", c)
		return
	}

	if err := dyUserService.UpdateUserIP(req.ID, req.BindIP); err != nil {
		response.FailWithMessage("更新IP失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("更新IP成功", c)
}

// UpdateCategory 更新用户分类
// @Tags      DyUser
// @Summary   更新用户分类
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      request.UpdateCategoryRequest  true  "用户ID和分类ID"
// @Success   200   {object}  response.Response{msg=string}  "更新成功"
// @Router    /douyin/user/update-category [post]
func (e *DyUserApi) UpdateCategory(c *gin.Context) {
	var req request.UpdateCategoryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage("参数错误", c)
		return
	}

	if err := dyUserService.UpdateCategory(req.ID, req.CategoryId); err != nil {
		response.FailWithMessage("更新分类失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("更新分类成功", c)
}

// BindDevice
// @Tags      DyUser
// @Summary   绑定设备到用户
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      request.BindDeviceRequest                                  true  "用户ID"
// @Success   200   {object}  response.Response{msg=string}                              "绑定成功"
// @Router    /douyin/user/bind-device [post]
func (e *DyUserApi) BindDevice(c *gin.Context) {
	var req request.BindDeviceRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage("参数解析失败", c)
		return
	}

	err = dyUserService.BindInfo(req.ID, req.RealName, req.Phone)
	if err != nil {
		response.FailWithMessage("操作失败: "+err.Error(), c)
		return
	}

	// 根据操作类型返回不同的成功消息
	var messages []string
	if req.RealName != nil {
		if *req.RealName != "" {
			messages = append(messages, "实名更新")
		} else {
			messages = append(messages, "实名清空")
		}
	}
	if req.Phone != nil {
		if *req.Phone != "" {
			messages = append(messages, "手机号绑定")
		} else {
			messages = append(messages, "手机号解绑")
		}
	}

	if len(messages) > 0 {
		response.OkWithMessage(fmt.Sprintf("%s成功", strings.Join(messages, "和")), c)
	} else {
		response.OkWithMessage("操作成功", c)
	}
}

// RefreshUserInfo
// @Tags      DyUser
// @Summary   刷新用户信息
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      common.GetById                                  true  "用户ID"
// @Success   200   {object}  response.Response{data=douyin.DyUser,msg=string}  "刷新成功"
// @Router    /douyin/user/refresh [post]
func (e *DyUserApi) RefreshUserInfo(c *gin.Context) {
	var req common.GetById
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage("参数解析失败", c)
		return
	}

	// 获取用户信息
	user, err := dyUserService.GetUserByID(uint(req.ID))
	if err != nil {
		response.FailWithMessage("获取用户信息失败: "+err.Error(), c)
		return
	}

	// 调用火苗API获取用户信息
	userInfo, err := awemeService.GetUserInfo(user.ImToken, user.BindIP)
	if err != nil {
		response.FailWithMessage("获取用户信息失败: "+err.Error(), c)
		return
	}

	// 更新用户信息
	avatarBytes, _ := json.Marshal(userInfo.User.Avatar168x168)
	avatar := string(avatarBytes)

	updateData := map[string]interface{}{
		"nickname":        userInfo.User.Nickname,
		"avatar":          avatar,
		"unique_id":       userInfo.User.UniqueId,
		"short_id":        userInfo.User.ShortId,
		"follower_count":  userInfo.User.FollowerCount,
		"total_favorited": userInfo.User.TotalFavorited,
		"account_region":  userInfo.User.AccountRegion,
		"province":        userInfo.User.Province,
		"city":            userInfo.User.City,
		"college_name":    userInfo.User.CollegeName,
		"bind_phone":      userInfo.User.BindPhone,
		"birthday":        userInfo.User.Birthday,
		"gender":          userInfo.User.Gender,
		"signature":       userInfo.User.Signature,
	}

	err = dyUserService.UpdateUserInfo(user.ID, updateData)
	if err != nil {
		response.FailWithMessage("更新用户信息失败: "+err.Error(), c)
		return
	}

	// 重新获取更新后的用户信息
	updatedUser, err := dyUserService.GetUserByID(user.ID)
	if err != nil {
		response.FailWithMessage("获取更新后的用户信息失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(updatedUser, "刷新成功", c)
}

// UpdateUserRemark 更新用户备注
// @Tags      DyUser
// @Summary   更新用户备注
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      request.UpdateUserRemarkRequest  true  "用户ID和备注信息"
// @Success   200   {object}  response.Response{msg=string}    "更新成功"
// @Router    /douyin/user/update-remark [post]
func (e *DyUserApi) UpdateUserRemark(c *gin.Context) {
	var req request.UpdateUserRemarkRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if err := dyUserService.UpdateUserRemark(req); err != nil {
		response.FailWithMessage("更新备注失败: "+err.Error(), c)
		return
	}
	response.OkWithMessage("更新备注成功", c)
}

// UpdateComment 用于更新用户评论设置
func (e *DyUserApi) UpdateComment(c *gin.Context) {
	var req request.UpdateCommentRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	sid := utils.GetUserID(c)
	user, err := dyUserService.GetUserByID(req.ID)
	if err != nil {
		response.FailWithMessage("获取用户信息失败: "+err.Error(), c)
		return
	}
	if user.SysUserId != int64(sid) {
		response.FailWithMessage("暂无编辑权限", c)
		return
	}

	updateData := map[string]interface{}{
		"comment_templates":    req.CommentTemplates,
		"comment_keyword":      req.CommentKeyword,
		"reply_templates":      req.ReplyTemplates,
		"comment_trace_status": req.CommentTraceStatus,
	}

	if err := dyUserService.UpdateUserInfo(req.ID, updateData); err != nil {
		response.FailWithMessage("编辑失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("评论设定配置成功", c)
}

// UpdateTalkAuthStatus 更新私信授权状态
// @Tags      DyUser
// @Summary   更新私信授权状态
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      request.UpdateTalkAuthRequest  true  "用户ID和授权状态"
// @Success   200   {object}  response.Response{msg=string}  "更新成功"
// @Router    /douyin/user/update-talk-auth [post]
func (e *DyUserApi) UpdateTalkAuthStatus(c *gin.Context) {
	var req request.UpdateTalkAuthRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	updateData := map[string]interface{}{
		"talk_auth_status": req.TalkAuthStatus,
	}

	if err := dyUserService.UpdateUserInfo(req.ID, updateData); err != nil {
		response.FailWithMessage("更新失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("私信授权状态更新成功", c)
}

// ClearTalkAuth 清空私信授权
// @Tags      DyUser
// @Summary   清空私信授权
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      request.ClearTalkAuthRequest  true  "用户ID和浏览器ID"
// @Success   200   {object}  response.Response{msg=string}  "清空成功"
// @Router    /douyin/user/clear-talk-auth [post]
func (e *DyUserApi) ClearTalkAuth(c *gin.Context) {
	var req request.ClearTalkAuthRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	// 获取用户信息
	_, err := dyUserService.GetUserByID(req.ID)
	if err != nil {
		response.FailWithMessage("获取用户信息失败: "+err.Error(), c)
		return
	}
	updateData := map[string]interface{}{
		"talk_auth_status": 0,
		"bite_browser_id":  "",
	}
	if err := dyUserService.UpdateUserInfo(req.ID, updateData); err != nil {
		response.FailWithMessage("更新失败: "+err.Error(), c)
		return
	}

	response.Ok(c)
	return
}

// UpdateBiteBrowserId 更新比特浏览器ID
// @Tags      DyUser
// @Summary   更新比特浏览器ID
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      request.UpdateBiteBrowserRequest  true  "用户ID和浏览器ID"
// @Success   200   {object}  response.Response{msg=string}     "更新成功"
// @Router    /douyin/user/update-bite-browser [post]
func (e *DyUserApi) UpdateBiteBrowserId(c *gin.Context) {
	var req request.UpdateBiteBrowserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	updateData := map[string]interface{}{
		"bite_browser_id": req.BiteBrowserId,
	}

	if err := dyUserService.UpdateUserInfo(req.ID, updateData); err != nil {
		response.FailWithMessage("更新失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("更新成功", c)
}

// UpdatePromotionLink 更新推广链接
// @Tags      DyUser
// @Summary   更新用户推广链接
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      request.UpdatePromotionLinkRequest  true  "用户ID和推广链接"
// @Success   200   {object}  response.Response{msg=string}       "更新成功"
// @Router    /douyin/user/update-promotion-link [post]
func (e *DyUserApi) UpdatePromotionLink(c *gin.Context) {
	var req request.UpdatePromotionLinkRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	updateData := map[string]interface{}{
		"promotion_link": req.PromotionLink,
	}

	if err := dyUserService.UpdateUserInfo(req.ID, updateData); err != nil {
		response.FailWithMessage("更新失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("推广链接更新成功", c)
}

// UpdateTraceStatus 更新追踪状态
// @Tags      DyUser
// @Summary   更新追踪状态
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      request.UpdateTraceStatusRequest  true  "用户ID和追踪状态"
// @Success   200   {object}  response.Response{msg=string}      "更新成功"
// @Router    /douyin/user/update-trace-status [post]
func (e *DyUserApi) UpdateTraceStatus(c *gin.Context) {
	var req request.UpdateTraceStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	updateData := map[string]interface{}{
		"comment_trace_status": req.CommentTraceStatus,
	}
	if err := dyUserService.UpdateUserInfo(req.ID, updateData); err != nil {
		response.FailWithMessage("更新失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("更新成功", c)
}

// 通过抖音号模糊查询用户
// @Tags      DyUser
// @Summary   通过抖音号模糊查询用户
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  query     request.SearchUserByShortId  true  "抖音号"
// @Success   200   {object}  response.Response{data=[]douyin.DyUser,msg=string}  "查询成功"
// @Router    /douyin/user/search-by-uniqueid [get]
func (e *DyUserApi) SearchUserByUniqueId(c *gin.Context) {
	var req request.SearchUserUniqueIdRequest
	err := c.ShouldBindQuery(&req) // 从查询参数中获取抖音号
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	//使用模糊匹配查询mysql中unique_id字段
	users, err := dyUserService.SearchUserByUniqueId(req.UniqueId)
	if err != nil {
		response.FailWithMessage("查询失败: "+err.Error(), c)
		return
	}
	response.OkWithDetailed(users, "查询成功", c)
}

// 通过代理ip端口查询用户
// @Tags      DyUser
// @Summary   通过代理ip端口查询IP信息
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  query     request.SearchUserByPortRequest  true  "端口号"
// @Success   200   {object}  response.Response{data=[]douyin.IpPool,msg=string}  "查询成功"
// @Router    /douyin/user/search-by-port [get]
func (e *DyUserApi) SearchUserByPort(c *gin.Context) {
	var req request.SearchUserByPortRequest
	err := c.ShouldBindQuery(&req) // 从查询参数中获取端口号
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	//使用模糊匹配查询mysql中ip字段
	ips, err := dyUserService.SearchUserByPort(req.Port)
	if err != nil {
		response.FailWithMessage("查询失败: "+err.Error(), c)
		return
	}
	response.OkWithDetailed(ips, "查询成功", c)
}

// 通过MAC地址查询IP
// @Tags      DyUser
// @Summary   通过MAC地址查询匹配的IP信息
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  query     request.SearchUserByMacRequest  true  "MAC地址"
// @Success   200   {object}  response.Response{data=[]douyin.IpPool,msg=string}  "查询成功"
// @Router    /douyin/user/search-by-mac [get]
func (e *DyUserApi) SearchUserByMac(c *gin.Context) {
	var req request.SearchUserByMacRequest
	err := c.ShouldBindQuery(&req) // 从查询参数中获取MAC地址
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	//通过MAC地址匹配IP信息
	ips, err := dyUserService.SearchUserByMac(req.Mac)
	if err != nil {
		response.FailWithMessage("查询失败: "+err.Error(), c)
		return
	}
	response.OkWithDetailed(ips, "查询成功", c)
}

// SaveUserSort 保存用户排序
// @Tags      DyUser
// @Summary   保存用户排序
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      request.SaveUserSortRequest  true  "用户ID列表"
// @Success   200   {object}  response.Response{msg=string}  "保存成功"
// @Router    /douyin/user/save-sort [post]
func (e *DyUserApi) SaveUserSort(c *gin.Context) {
	var req request.SaveUserSortRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 获取当前用户ID
	currentUserID := utils.GetUserID(c)

	// 从查询参数获取分类ID（可选）
	var categoryId *uint
	if categoryIdStr := c.Query("categoryId"); categoryIdStr != "" {
		var catId uint
		if _, err := fmt.Sscanf(categoryIdStr, "%d", &catId); err == nil && catId > 0 {
			categoryId = &catId
		}
	}

	// 保存排序
	if err := dyUserService.SaveUserSort(currentUserID, categoryId, req.UserIds); err != nil {
		response.FailWithMessage("保存排序失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("保存排序成功", c)
}

// GetUserSort 获取用户排序
// @Tags      DyUser
// @Summary   获取用户排序
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  query     request.GetUserSortRequest  true  "查询参数"
// @Success   200   {object}  response.Response{data=[]uint,msg=string}  "获取成功"
// @Router    /douyin/user/get-sort [get]
func (e *DyUserApi) GetUserSort(c *gin.Context) {
	var req request.GetUserSortRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 获取当前用户ID
	currentUserID := utils.GetUserID(c)

	// 获取排序
	userIds, err := dyUserService.GetUserSort(currentUserID, req.CategoryId)
	if err != nil {
		response.FailWithMessage("获取排序失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(userIds, "获取成功", c)
}
