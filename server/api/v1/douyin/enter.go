package douyin

import "github.com/flipped-aurora/gin-vue-admin/server/service"

type ApiGroup struct {
	DyUserCategoryApi
	DyUserApi
	DyUserForMoreApi
	DyAuthUserApi
	DyProductApi
	DeviceInfoApi
	DyIPApi
	FlameUserApi
	FlamePlayerApi
	FlamePlayerCollectionApi
	DyProductSelectionApi
	PhoneBalanceApi
	DyTopCommentApi
	DyChatApi
	DyAwemeApi
}

var (
	// 抖音服务组
	dyAuthUserService         = service.ServiceGroupApp.DouyinServiceGroup.DyAuthUserService
	dyUserCategoryService     = service.ServiceGroupApp.DouyinServiceGroup.DyUserCategoryService
	dyUserService             = service.ServiceGroupApp.DouyinServiceGroup.DyUserService
	dyProductService          = service.ServiceGroupApp.DouyinServiceGroup.DyProductService
	dyProductSelectionService = service.ServiceGroupApp.DouyinServiceGroup.DyProductSelectionService
	deviceInfoService         = service.ServiceGroupApp.DouyinServiceGroup.DeviceInfoService
	dyIPService               = service.ServiceGroupApp.DouyinServiceGroup.DyIPService
	dyTopCommentService       = service.ServiceGroupApp.DouyinServiceGroup.DyTopCommentService
	dyAwemeService            = service.ServiceGroupApp.DouyinServiceGroup.DyAwemeService

	// 火苗相关服务
	flameUserService             = service.ServiceGroupApp.DouyinServiceGroup.FlameUserService
	flamePlayerService           = service.ServiceGroupApp.DouyinServiceGroup.FlamePlayerService
	flamePlayerCollectionService = service.ServiceGroupApp.DouyinServiceGroup.FlamePlayerCollectionService

	// 其他服务
	phoneBalanceService = service.ServiceGroupApp.DouyinServiceGroup.PhoneBalanceService

	// API服务
	awemeService                = &service.ServiceGroupApp.DouyinServiceGroup.AwemeApiService
	moreApiService              = &service.ServiceGroupApp.DouyinServiceGroup.MoreApiService
	moreCreatorApiService       = &service.ServiceGroupApp.DouyinServiceGroup.MoreCreatorApiService
	moreCreatorCustomApiService = &service.ServiceGroupApp.DouyinServiceGroup.MoreCreatorCustomApiService
	flameApiService             = &service.ServiceGroupApp.DouyinServiceGroup.FlameApiService

	// 系统服务组
	authUserService = service.ServiceGroupApp.SystemServiceGroup.UserService
	userService     = service.ServiceGroupApp.SystemServiceGroup.UserService

	videoService = service.ServiceGroupApp.CreativeServiceGroup.VideoService
)
