package creative

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/creative"
	creativeReq "github.com/flipped-aurora/gin-vue-admin/server/model/creative/request"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type VideoCategoryApi struct{}

// CreateVideoCategory
// @Tags      VideoCategory
// @Summary   创建视频分类
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      creative.VideoCategory                true  "视频分类信息"
// @Success   200   {object}  response.Response{msg=string}        "创建成功"
// @Router    /video-category/create [post]
func (api *VideoCategoryApi) CreateVideoCategory(c *gin.Context) {
	var category creative.VideoCategory
	err := c.ShouldBindJSON(&category)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 设置创建人
	userID := utils.GetUserID(c)
	category.CreatedBy = userID

	err = videoCategoryService.CreateVideoCategory(&category)
	if err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败", c)
		return
	}
	response.OkWithMessage("创建成功", c)
}

// DeleteVideoCategory
// @Tags      VideoCategory
// @Summary   删除视频分类
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      creative.VideoCategory                true  "视频分类ID"
// @Success   200   {object}  response.Response{msg=string}        "删除成功"
// @Router    /video-category/delete [delete]
func (api *VideoCategoryApi) DeleteVideoCategory(c *gin.Context) {
	var category creative.VideoCategory
	err := c.ShouldBindJSON(&category)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = videoCategoryService.DeleteVideoCategory(category.ID)
	if err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

// UpdateVideoCategory
// @Tags      VideoCategory
// @Summary   更新视频分类
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      creative.VideoCategory                true  "视频分类信息"
// @Success   200   {object}  response.Response{msg=string}        "更新成功"
// @Router    /video-category/update [put]
func (api *VideoCategoryApi) UpdateVideoCategory(c *gin.Context) {
	var category creative.VideoCategory
	err := c.ShouldBindJSON(&category)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = videoCategoryService.UpdateVideoCategory(&category)
	if err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
		return
	}
	response.OkWithMessage("更新成功", c)
}

// GetVideoCategory
// @Tags      VideoCategory
// @Summary   获取视频分类信息
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  query     creative.VideoCategory                true  "视频分类ID"
// @Success   200   {object}  response.Response{data=creative.VideoCategory,msg=string}  "获取成功"
// @Router    /video-category/info [get]
func (api *VideoCategoryApi) GetVideoCategory(c *gin.Context) {
	var category creative.VideoCategory
	err := c.ShouldBindQuery(&category)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	data, err := videoCategoryService.GetVideoCategory(category.ID)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
		return
	}
	response.OkWithDetailed(gin.H{"category": data}, "获取成功", c)
}

// GetVideoCategoryList
// @Tags      VideoCategory
// @Summary   分页获取视频分类列表
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  query     creativeReq.VideoCategorySearch      true  "页码, 每页大小, 搜索关键词"
// @Success   200   {object}  response.Response{data=response.PageResult,msg=string}  "获取成功"
// @Router    /video-category/list [get]
func (api *VideoCategoryApi) GetVideoCategoryList(c *gin.Context) {
	var pageInfo creativeReq.VideoCategorySearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 获取当前用户ID
	currentUserID := utils.GetUserID(c)

	// 获取当前用户及其下级用户的ID列表
	userIds, err := userService.GetSubordinateUserIds(currentUserID)
	if err != nil {
		response.FailWithMessage("获取用户权限失败: "+err.Error(), c)
		return
	}
	// 添加当前用户ID
	userIds = append(userIds, currentUserID)

	list, total, err := videoCategoryService.GetVideoCategoryList(pageInfo, userIds)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
		return
	}
	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}
