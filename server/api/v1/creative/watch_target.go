package creative

import (
	"strconv"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	creativeModel "github.com/flipped-aurora/gin-vue-admin/server/model/creative"
	"github.com/flipped-aurora/gin-vue-admin/server/model/creative/request"
	creativeResponse "github.com/flipped-aurora/gin-vue-admin/server/model/creative/response"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	utilsAi "github.com/flipped-aurora/gin-vue-admin/server/utils/ai"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type WatchTargetApi struct{}

// AddWatchTarget
// @Tags      WatchTarget
// @Summary   添加对标用户
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      request.WatchTargetUserAdd  true  "对标用户唯一ID"
// @Success   200   {object}  response.Response{msg=string}  "添加成功"
// @Router    /creative/watch-target/add [post]
func (api *WatchTargetApi) AddWatchTarget(c *gin.Context) {
	var addReq request.WatchTargetUserAdd
	err := c.ShouldBindJSON(&addReq)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 获取当前用户ID
	userId := utils.GetUserID(c)

	// 创建对标用户
	user, err := watchTargetUserService.CreateWatchTargetUser(addReq.UniqueId, userId)
	if err != nil {
		global.GVA_LOG.Error("创建对标用户失败", zap.Error(err))
		response.FailWithMessage("创建对标用户失败: "+err.Error(), c)
		return
	}

	// 添加对标用户作品
	err = watchTargetPostService.AsyncAddWatchTargetPosts(user, userId)
	if err != nil {
		global.GVA_LOG.Error("添加对标用户作品失败", zap.Error(err))
		response.FailWithMessage("添加对标用户作品失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("添加成功", c)
}

// GetWatchTargetUserList
// @Tags      WatchTarget
// @Summary   获取对标用户列表
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  query     request.WatchTargetUserSearch  true  "查询参数"
// @Success   200   {object}  response.Response{data=creativeResponse.WatchTargetUserResponse,msg=string}  "获取成功"
// @Router    /creative/watch-target/user-list [get]
func (api *WatchTargetApi) GetWatchTargetUserList(c *gin.Context) {
	var searchInfo request.WatchTargetUserSearch
	err := c.ShouldBindQuery(&searchInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if searchInfo.Page == 0 {
		searchInfo.Page = 1
	}
	if searchInfo.PageSize == 0 {
		searchInfo.PageSize = 10
	}

	// 获取当前用户ID
	currentUserID := utils.GetUserID(c)

	// 获取当前用户及其下级用户的ID列表
	userIds, err := userService.GetSubordinateUserIds(currentUserID)
	if err != nil {
		response.FailWithMessage("获取用户权限失败: "+err.Error(), c)
		return
	}
	// 添加当前用户ID
	userIds = append(userIds, currentUserID)

	list, total, err := watchTargetUserService.GetWatchTargetUserList(searchInfo, userIds)
	if err != nil {
		global.GVA_LOG.Error("获取失败", zap.Error(err))
		response.FailWithMessage("获取失败", c)
		return
	}
	response.OkWithDetailed(creativeResponse.WatchTargetUserResponse{
		List:     list,
		Total:    total,
		Page:     searchInfo.Page,
		PageSize: searchInfo.PageSize,
	}, "获取成功", c)
}

// GetWatchTargetPostList
// @Tags      WatchTarget
// @Summary   获取对标用户作品列表
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  query     request.WatchTargetPostSearch  true  "查询参数"
// @Success   200   {object}  response.Response{data=creativeResponse.WatchTargetPostResponse,msg=string}  "获取成功"
// @Router    /creative/watch-target/post-list [get]
func (api *WatchTargetApi) GetWatchTargetPostList(c *gin.Context) {
	var searchInfo request.WatchTargetPostSearch
	err := c.ShouldBindQuery(&searchInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if searchInfo.Page == 0 {
		searchInfo.Page = 1
	}
	if searchInfo.PageSize == 0 {
		searchInfo.PageSize = 10
	}
	list, total, err := watchTargetPostService.GetWatchTargetPostList(searchInfo)
	if err != nil {
		global.GVA_LOG.Error("获取失败", zap.Error(err))
		response.FailWithMessage("获取失败", c)
		return
	}
	response.OkWithDetailed(creativeResponse.WatchTargetPostResponse{
		List:     list,
		Total:    total,
		Page:     searchInfo.Page,
		PageSize: searchInfo.PageSize,
	}, "获取成功", c)
}

// GetWatchTargetPostDetail
// @Tags      WatchTarget
// @Summary   获取对标用户作品详情
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     id  path     int  true  "作品ID"
// @Success   200   {object}  response.Response{data=creativeResponse.WatchTargetPostDetailResponse,msg=string}  "获取成功"
// @Router    /creative/watch-target/post-detail/{id} [get]
func (api *WatchTargetApi) GetWatchTargetPostDetail(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		response.FailWithMessage("参数错误", c)
		return
	}
	post, err := watchTargetPostService.GetWatchTargetPostDetail(uint(id))
	if err != nil {
		global.GVA_LOG.Error("获取失败", zap.Error(err))
		response.FailWithMessage("获取失败", c)
		return
	}
	response.OkWithDetailed(creativeResponse.WatchTargetPostDetailResponse{
		Post: post,
	}, "获取成功", c)
}

// DeleteWatchTargetUser
// @Tags      WatchTarget
// @Summary   删除对标用户
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     id  path     int  true  "用户ID"
// @Success   200   {object}  response.Response{msg=string}  "删除成功"
// @Router    /creative/watch-target/delete/{id} [delete]
func (api *WatchTargetApi) DeleteWatchTargetUser(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		response.FailWithMessage("参数错误", c)
		return
	}
	if err := watchTargetUserService.DeleteWatchTargetUser(uint(id)); err != nil {
		global.GVA_LOG.Error("删除失败", zap.Error(err))
		response.FailWithMessage("删除失败", c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

// GenerateFromPost
// @Tags      WatchTarget
// @Summary   从对标作品智能生成内容
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      request.GenerateFromPostRequest  true  "生成参数"
// @Success   200   {object}  response.Response{msg=string}  "生成成功"
// @Router    /creative/watch-target/generate [post]
func (api *WatchTargetApi) GenerateFromPost(c *gin.Context) {
	var req request.GenerateFromPostRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 获取对标用户作品
	post, err := watchTargetPostService.GetWatchTargetPostDetail(req.ID)
	if err != nil {
		global.GVA_LOG.Error("获取失败", zap.Error(err))
		response.FailWithMessage("获取失败", c)
		return
	}
	if post.FileUrl == "" {
		response.FailWithMessage("作品不存在视频", c)
		return
	}

	if post.GenerateState == creativeModel.GenerateStateGenerating {
		response.FailWithMessage("作品正在生成中，请稍后再试", c)
		return
	}

	if post.GenerateState == creativeModel.GenerateStateSuccess {
		response.FailWithMessage("作品已生成，请刷新页面查看结果", c)
		return
	}

	// 获取AI模型
	aiModel, err := aiModelService.GetAiModel(req.AIModelID)
	if err != nil {
		global.GVA_LOG.Error("获取AI模型失败", zap.Error(err))
		response.FailWithMessage("获取AI模型失败", c)
		return
	}

	// 获取音乐ID
	var musicId string
	if req.MusicFrom == creativeModel.MusicFromPost {
		// 作品自带音乐
		musicId = post.MusicId
	} else {
		// AI模型自带音乐
		musicId = aiModel.MusicID
	}

	// 生成换脸视频
	processor, err := utilsAi.GetModelFactory().GetProcessor(utilsAi.ModelTypeRunningHub)
	if err != nil {
		global.GVA_LOG.Error("获取模型处理器失败", zap.Error(err))
		response.FailWithMessage("获取模型处理器失败", c)
		return
	}

	taskId, err := processor.CreateTask(utilsAi.TaskTypeVideoSwapFace, utilsAi.VideoSwapFaceTaskRequest{
		SourceImage: aiModel.FaceImageURL,
		VideoURL:    post.FileUrl,
	})
	if err != nil {
		global.GVA_LOG.Error("创建任务失败", zap.Error(err))
		response.FailWithMessage("创建任务失败", c)
		return
	}

	// 更新作品
	watchTargetPostService.UpdateWatchTargetPost(req.ID, map[string]interface{}{
		"generate_state": creativeModel.GenerateStateGenerating,
	})

	// 先查询是否已存在视频记录
	existingVideo, err := videoService.GetVideoByPostId(req.ID)
	if err == nil && existingVideo.ID > 0 {
		// 如果存在，则更新task_id
		existingVideo.TaskId = taskId
		existingVideo.Status = creativeModel.VideoStatusGenerating
		videoService.UpdateVideo(existingVideo)
	} else {
		// 如果不存在，则创建新视频
		videoService.CreateVideo(&creativeModel.Video{
			CategoryId: req.VideoCategoryID,
			Title:      req.Title,
			ProductId:  req.ProductID,
			Status:     creativeModel.VideoStatusGenerating,
			Source:     creativeModel.VideoSourceAI,
			PostId:     req.ID,
			MusicId:    musicId,
			Topic:      post.Topic,
			Url:        "", // 视频链接，任务完成后更新
			TaskId:     taskId,
			CreatedBy:  utils.GetUserID(c),
		})
	}

	response.OkWithMessage("智能生成成功，请稍后刷新页面查看结果", c)
}

// UpdateWatchTargetUserStatus
// @Tags      WatchTarget
// @Summary   更新对标用户状态
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     id      path    int                                true   "对标用户ID"
// @Param     data    body    request.UpdateStatusRequest      true   "更新状态"
// @Success   200     {object}  response.Response{msg=string}  "更新成功"
// @Router    /creative/watch-target/status/{id} [put]
func (api *WatchTargetApi) UpdateWatchTargetUserStatus(c *gin.Context) {
	var updateReq request.UpdateStatusRequest
	err := c.ShouldBindJSON(&updateReq)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		response.FailWithMessage("参数错误", c)
		return
	}

	err = watchTargetUserService.UpdateWatchTargetUserStatus(uint(id), updateReq.Status, utils.GetUserID(c))
	if err != nil {
		global.GVA_LOG.Error("更新状态失败!", zap.Error(err))
		response.FailWithMessage("更新状态失败", c)
		return
	}
	response.OkWithMessage("更新状态成功", c)
}

// RefreshWatchTargetUser
// @Tags      WatchTarget
// @Summary   刷新对标用户信息
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     id  path     int  true  "用户ID"
// @Success   200   {object}  response.Response{msg=string}  "刷新成功"
// @Router    /creative/watch-target/refresh/{id} [post]
func (api *WatchTargetApi) RefreshWatchTargetUser(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		response.FailWithMessage("参数错误", c)
		return
	}

	if err := watchTargetUserService.RefreshWatchTargetUser(uint(id)); err != nil {
		global.GVA_LOG.Error("刷新失败", zap.Error(err))
		response.FailWithMessage("刷新失败", c)
		return
	}
	response.OkWithMessage("刷新成功", c)
}

// RefreshWatchTargetUserPosts
// @Tags      WatchTarget
// @Summary   刷新对标用户作品
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     id  path     int  true  "用户ID"
// @Success   200   {object}  response.Response{msg=string}  "刷新成功"
// @Router    /creative/watch-target/refresh-posts/{id} [post]
func (api *WatchTargetApi) RefreshWatchTargetUserPosts(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		response.FailWithMessage("参数错误", c)
		return
	}

	if err := watchTargetUserService.RefreshWatchTargetUserPosts(uint(id)); err != nil {
		global.GVA_LOG.Error("刷新作品失败", zap.Error(err))
		response.FailWithMessage("刷新作品失败", c)
		return
	}
	response.OkWithMessage("刷新作品成功", c)
}

// RefreshWatchTargetPost
// @Tags      WatchTarget
// @Summary   刷新单个作品
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     id  path     int  true  "作品ID"
// @Success   200   {object}  response.Response{msg=string}  "刷新成功"
// @Router    /creative/watch-target/refresh-post/{id} [post]
func (api *WatchTargetApi) RefreshWatchTargetPost(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		response.FailWithMessage("参数错误", c)
		return
	}

	if err := watchTargetPostService.RefreshWatchTargetPost(uint(id)); err != nil {
		global.GVA_LOG.Error("刷新作品失败", zap.Error(err))
		response.FailWithMessage("刷新作品失败", c)
		return
	}
	response.OkWithMessage("刷新作品成功", c)
}
