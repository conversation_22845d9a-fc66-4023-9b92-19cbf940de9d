package creative

import (
	"strconv"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/creative"
	"github.com/flipped-aurora/gin-vue-admin/server/model/creative/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/douyin"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
)

type AutoPublishVideoApi struct{}

// GetByDyUserID
// @Tags AutoPublishVideo
// @Summary 获取用户自动发布计划
// @Security ApiKeyAuth
// @Produce  application/json
// @Param dyUserId query string true "抖音用户ID"
// @Success 200 {object} response.Response{data=[]creative.AutoPublishVideo}
// @Router /autoPublishVideo/getByDyUser [get]
func (api *AutoPublishVideoApi) List(c *gin.Context) {
	var req request.GetAutoPublishVideoListRequest
	// 绑定查询参数到 req 结构体
	err := c.ShouldBindQuery(&req)
	if err != nil {
		// 参数绑定失败，返回错误信息
		response.FailWithMessage(err.Error(), c)
		return
	}

	dyUserIdUint, err := strconv.ParseUint(req.DyUserID, 10, 64)
	if err != nil {
		response.FailWithMessage("抖音用户ID格式错误", c)
		return
	}
	list, err := autoPublishVideoService.GetListByDyUserId(uint(dyUserIdUint))
	// 查询失败，返回错误信息
	if err != nil {
		response.FailWithMessage("查询失败: "+err.Error(), c)
		return
	}
	// 查询成功，返回视频列表

	response.OkWithData(list, c)
}

// Save
// @Tags AutoPublishVideo
// @Summary 批量创建自动发布计划
// @Security ApiKeyAuth
// @Accept json
// @Produce json
// @Param data body creative.BatchAutoPublishVideoRequest true "请求参数"
// @Success 200 {object} response.Response
// @Router /autoPublishVideo/batchCreate [post]
func (api *AutoPublishVideoApi) Save(c *gin.Context) {
	var req request.SaveAutoPublishVideoRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if req.DyUserId <= 0 || len(req.List) == 0 {
		response.FailWithMessage("参数错误:请至少选择一个时段", c)
		return
	}

	sysUserId := utils.GetUserID(c)
	dyUser, err := dyUserService.GetUserByID(uint(req.DyUserId))
	if err != nil {
		response.FailWithMessage("查询抖音用户信息失败: "+err.Error(), c)
		return
	}
	if uint(dyUser.SysUserId) != sysUserId {
		response.FailWithMessage("您没有操作该账号的权限", c)
		return
	}

	videoCategoryId := 0
	var list []creative.AutoPublishVideo
	for _, item := range req.List {
		task := creative.AutoPublishVideo{
			SysUserId:       int64(sysUserId),
			DyUserId:        int64(req.DyUserId),
			Weekday:         item.Weekday,
			Hour:            item.Hour,
			Type:            item.Type,
			VideoCategoryId: item.VideoCategoryId,
		}
		// 这里需要根据item的具体结构补充其他字段的赋值
		list = append(list, task)
		videoCategoryId = item.VideoCategoryId
	}

	// 开启事务
	tx := global.GVA_DB.Begin()
	if tx.Error != nil {
		response.FailWithMessage("开启事务失败: "+tx.Error.Error(), c)
		return
	}
	if err = tx.Unscoped().Where("dy_user_id = ?", req.DyUserId).
		Delete(creative.AutoPublishVideo{}).Error; err != nil {
		response.FailWithMessage("删除旧计划失败: "+err.Error(), c)
		tx.Rollback()
		return
	}

	if err = tx.Model(creative.AutoPublishVideo{}).Create(&list).Error; err != nil {
		response.FailWithMessage("创建失败: "+err.Error(), c)
		tx.Rollback()
		return
	}

	//当dyUser.AutoPublishStatus 为0,将其设置为1
	if dyUser.AutoPublishStatus != 1 || dyUser.VideoCategoryId != videoCategoryId {
		updateData := map[string]interface{}{
			"auto_publish_status": 1,
			"video_category_id":   videoCategoryId,
		}
		if err = tx.Model(&douyin.DyUser{}).
			Where("id =?", dyUser.ID).
			Updates(updateData).Error; err != nil {
			tx.Rollback()
			response.FailWithMessage("设置用户状态失败: "+err.Error(), c)
			return
		}
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		response.FailWithMessage("提交事务失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("设置成功", c)
}

// 修改发布状态
// @Tags AutoPublishVideo
// @Summary 修改发布状态
// @Security ApiKeyAuth
// @Produce  application/json
// @Param data body request.ChangeStatusRequest true "请求参数"
// @Success 200 {object} response.Response
// @Router /autoPublishVideo/ChangeStatus [post]
// 修改发布状态
func (api *AutoPublishVideoApi) ChangeStatus(c *gin.Context) {
	var req request.ChangeStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 1. 验证用户是否存在
	dyUser, err := dyUserService.GetUserByID(uint(req.DyUserId))
	if err != nil || dyUser.ID == 0 {
		response.FailWithMessage("抖音用户不存在", c)
		return
	}

	// 2. 检查是否有发布记录
	var exist bool
	if err := global.GVA_DB.Model(&creative.AutoPublishVideo{}).
		Select("count(*) > 0").
		Where("dy_user_id = ?", req.DyUserId).
		Find(&exist).Error; err != nil || !exist {
		response.FailWithMessage("尚未设置发布规则", c)
		return
	}

	// 开启事务
	tx := global.GVA_DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 3. 根据opt执行操作
	switch req.Opt {
	case 1, 2: // 启用/暂停
		// 更新auto_publish_video状态
		if err := tx.Model(&creative.AutoPublishVideo{}).
			Where("dy_user_id = ?", req.DyUserId).
			Update("status", req.Opt).Error; err != nil {
			tx.Rollback()
			response.FailWithMessage("状态更新失败: "+err.Error(), c)
			return
		}

		// 更新dy_user状态
		if err := tx.Model(&douyin.DyUser{}).
			Where("id = ?", req.DyUserId).
			Update("auto_publish_status", req.Opt).Error; err != nil {
			tx.Rollback()
			response.FailWithMessage("用户状态更新失败: "+err.Error(), c)
			return
		}

	case 3: // 删除
		// 删除所有发布规则
		if err := tx.Where("dy_user_id = ?", req.DyUserId).
			Delete(&creative.AutoPublishVideo{}).Error; err != nil {
			tx.Rollback()
			response.FailWithMessage("删除规则失败: "+err.Error(), c)
			return
		}

		// 重置用户状态
		if err := tx.Model(&douyin.DyUser{}).
			Where("id = ?", req.DyUserId).
			Update("auto_publish_status", 0).Error; err != nil {
			tx.Rollback()
			response.FailWithMessage("用户状态重置失败: "+err.Error(), c)
			return
		}

	default:
		response.FailWithMessage("无效的操作类型", c)
		return
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		response.FailWithMessage("操作失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("操作成功", c)
}

// 分页获取发布记录
// @Tags AutoPublishVideoRecord
// @Summary 分页获取发布记录
// @Security ApiKeyAuth
// @Produce  application/json
// @Param data body request.GetAutoPublishVideoLogRequest true "请求参数"
// @Success 200 {object} response.Response{data=response.PageResult}
// @Router /autoPublishVideo/log [Get]
func (api *AutoPublishVideoApi) GetAutoPublishVideoLogList(c *gin.Context) {
	var req request.GetAutoPublishVideoLogSearch
	if err := c.ShouldBindQuery(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 获取当前用户ID
	currentUserID := utils.GetUserID(c)

	// 获取当前用户及其下级用户的ID列表
	userIds, err := userService.GetSubordinateUserIds(currentUserID)
	if err != nil {
		response.FailWithMessage("获取用户权限失败: "+err.Error(), c)
		return
	}
	// 添加当前用户ID
	userIds = append(userIds, currentUserID)
	req.SysUserIds = userIds

	list, total, err := autoPublishVideoRecordService.GetAutoPublishVideoLogPageList(req)
	if err != nil {
		response.FailWithMessage("查询失败: "+err.Error(), c)
		return
	}
	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, "获取成功", c)
}
