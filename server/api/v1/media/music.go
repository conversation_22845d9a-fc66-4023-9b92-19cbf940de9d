package media

import (
	"path/filepath"
	"strconv"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	mediaModel "github.com/flipped-aurora/gin-vue-admin/server/model/media"
	mediaReq "github.com/flipped-aurora/gin-vue-admin/server/model/media/request"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type MediaMusicApi struct{}

// UploadMusic 上传音乐
// @Tags Media.Music
// @Summary 上传音乐文件
// @Security ApiKeyAuth
// @accept multipart/form-data
// @Produce application/json
// @Param file formData file true "音乐文件"
// @Param name formData string true "音乐名称"
// @Param classId formData int false "分类ID"
// @Success 200 {object} response.Response{data=mediaModel.Music,msg=string} "上传音乐"
// @Router /media/upload [post]
func (api *MediaMusicApi) UploadMusic(c *gin.Context) {
	form, err := c.MultipartForm()
	if err != nil {
		global.GVA_LOG.Error("接收文件失败!", zap.Error(err))
		response.FailWithMessage("接收文件失败", c)
		return
	}

	files := form.File["file"] // 获取所有名为 "file" 的文件
	if len(files) == 0 {
		response.FailWithMessage("没有找到上传的文件", c)
		return
	}

	classIdStr := c.Request.FormValue("classId") // 从表单获取 classId
	classIdUint, _ := strconv.Atoi(classIdStr)
	userId := utils.GetUserID(c)
	var uploadedMusics []mediaModel.Music
	var uploadErrors []string

	for _, header := range files {
		// 默认使用文件名作为音乐名称 (去除后缀)
		fileName := header.Filename
		name := fileName[:len(fileName)-len(filepath.Ext(fileName))]

		music, err := musicService.UploadMusicFile(header, name, uint(classIdUint), userId)
		if err != nil {
			global.GVA_LOG.Error("上传音乐失败!", zap.String("filename", header.Filename), zap.Error(err))
			uploadErrors = append(uploadErrors, header.Filename+": "+err.Error())
		} else {
			uploadedMusics = append(uploadedMusics, *music)
		}
	}

	if len(uploadErrors) > 0 {
		response.FailWithDetailed(gin.H{"uploaded": uploadedMusics, "errors": uploadErrors}, "部分文件上传失败", c)
		return
	}

	response.OkWithDetailed(gin.H{"uploaded": uploadedMusics}, "上传成功", c)
}

// GetMusicList 获取音乐列表
// @Tags Media.Music
// @Summary 获取音乐列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param page query int false "页码"
// @Param pageSize query int false "每页数量"
// @Param keyword query string false "关键词"
// @Param categoryId query int false "分类ID"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取音乐列表"
// @Router /media/music/list [get]
func (api *MediaMusicApi) GetMusicList(c *gin.Context) {
	var pageInfo mediaReq.MusicSearch
	pageInfo.Page, _ = strconv.Atoi(c.DefaultQuery("page", "1"))
	pageInfo.PageSize, _ = strconv.Atoi(c.DefaultQuery("pageSize", "10"))
	pageInfo.Keyword = c.Query("keyword")
	categoryId, _ := strconv.Atoi(c.DefaultQuery("categoryId", "0"))
	pageInfo.CategoryId = uint(categoryId)

	userId := utils.GetUserID(c)
	list, total, err := musicService.GetMusicList(userId, pageInfo)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
		return
	}

	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// DeleteMusic 删除音乐
// @Tags Media.Music
// @Summary 删除音乐
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param id path int true "音乐ID"
// @Success 200 {object} response.Response{msg=string} "删除音乐"
// @Router /media/music/{id} [delete]
func (api *MediaMusicApi) DeleteMusic(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		response.FailWithMessage("无效的ID", c)
		return
	}

	userId := utils.GetUserID(c)
	err = musicService.DeleteMusic(uint(id), userId)
	if err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
		return
	}

	response.OkWithMessage("删除成功", c)
}

// EditMusicName 编辑音乐名称
// @Tags Media.Music
// @Summary 编辑音乐名称
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param id path int true "音乐ID"
// @Param data body map[string]string true "名称"
// @Success 200 {object} response.Response{msg=string} "编辑音乐名称"
// @Router /media/music/{id}/name [patch]
func (api *MediaMusicApi) EditMusicName(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		response.FailWithMessage("无效的ID", c)
		return
	}

	var reqData struct {
		Name string `json:"name"`
	}
	if err := c.ShouldBindJSON(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	music := &mediaModel.Music{}
	music.GVA_MODEL.ID = uint(id)
	music.Name = reqData.Name

	err = musicService.EditMusicName(music)
	if err != nil {
		global.GVA_LOG.Error("编辑失败!", zap.Error(err))
		response.FailWithMessage("编辑失败", c)
		return
	}

	response.OkWithMessage("编辑成功", c)
}

// ImportMusicURL 导入音乐URL
// @Tags Media.Music
// @Summary 导入音乐URL
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body []mediaModel.Music true "音乐列表"
// @Success 200 {object} response.Response{msg=string} "导入音乐URL"
// @Router /media/music/import [post]
func (api *MediaMusicApi) ImportMusicURL(c *gin.Context) {
	var musics []mediaModel.Music
	err := c.ShouldBindJSON(&musics)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if len(musics) == 0 {
		response.FailWithMessage("没有音乐数据", c)
		return
	}

	userId := utils.GetUserID(c)
	// 设置创建者ID
	for i := range musics {
		musics[i].CreatorId = userId
	}

	err = musicService.ImportMusicURLs(musics)
	if err != nil {
		global.GVA_LOG.Error("导入失败!", zap.Error(err))
		response.FailWithMessage("导入失败", c)
		return
	}

	response.OkWithMessage("导入成功", c)
}

// EditMusicCategory 修改音乐分类
// @Tags Media.Music
// @Summary 修改音乐分类
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param id path int true "音乐ID"
// @Param data body map[string]uint true "分类ID"
// @Success 200 {object} response.Response{msg=string} "修改音乐分类"
// @Router /media/music/{id}/category [patch]
func (api *MediaMusicApi) EditMusicCategory(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		response.FailWithMessage("无效的ID", c)
		return
	}

	var reqData struct {
		CategoryId uint `json:"categoryId"`
	}
	if err := c.ShouldBindJSON(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	music := &mediaModel.Music{}
	music.GVA_MODEL.ID = uint(id)
	music.CategoryId = reqData.CategoryId

	err = musicService.EditMusicCategory(music)
	if err != nil {
		global.GVA_LOG.Error("修改分类失败!", zap.Error(err))
		response.FailWithMessage("修改分类失败", c)
		return
	}

	response.OkWithMessage("修改分类成功", c)
}
