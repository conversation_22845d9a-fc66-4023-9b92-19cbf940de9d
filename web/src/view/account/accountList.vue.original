<template>
  <div v-loading.fullscreen.lock="fullscreenLoading">
    <div class="flex flex-col md:flex-row gap-6 p-4">
      <!-- 左侧分类树 -->
      <div
        class="w-full md:w-72 lg:w-80 bg-white text-slate-700 dark:text-slate-400 dark:bg-slate-900 rounded-lg shadow-sm p-4"
      >
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-medium">账号分类</h3>
        </div>
        <el-scrollbar style="height: calc(100vh - 320px)">
          <el-tree
            :data="categories"
            node-key="id"
            :props="defaultProps"
            @node-click="handleNodeClick"
            default-expand-all
            highlight-current
          >
            <template #default="{ data }">
              <div class="flex items-center justify-between w-full py-1">
                <div class="truncate" :class="search.categoryId === data.ID ? 'text-blue-500 font-bold' : ''">
                  {{ data.name }}
                </div>
                <el-dropdown>
                  <el-icon class="ml-3 cursor-pointer" v-if="data.ID > 0">
                    <MoreFilled />
                  </el-icon>
                  <el-icon class="ml-3 cursor-pointer" v-else>
                    <Plus />
                  </el-icon>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item @click="addCategoryFun(data)">添加分类</el-dropdown-item>
                      <el-dropdown-item @click="editCategory(data)" v-if="data.ID > 0">编辑分类</el-dropdown-item>
                      <el-dropdown-item @click="deleteCategoryFun(data.ID)" v-if="data.ID > 0"
                        >删除分类</el-dropdown-item
                      >
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </template>
          </el-tree>
        </el-scrollbar>
      </div>

      <!-- 右侧内容区 -->
      <div class="flex-1 bg-white text-slate-700 dark:text-slate-400 dark:bg-slate-900 rounded-lg shadow-sm">
        <div class="gva-table-box mt-0 mb-0 p-4">
          <warning-bar :title="'账号授权请先选择分类再扫码：抖音--我--右上角菜单--我的二维码--扫一扫'" class="mb-4" />

          <!-- 搜索和操作区域 -->
          <div class="flex flex-wrap items-center gap-3 mb-4">
            <el-button type="primary" icon="Key" @click="showSearchUniqueIdDialog" class="flex-none">
              账号授权
            </el-button>
            <div class="flex-1 flex flex-wrap gap-3">
              <el-input
                v-model="search.nickname"
                placeholder="请输入用户昵称"
                class="w-full sm:w-64 md:w-56 lg:w-64"
                clearable
              />
              <el-input
                v-model="search.uniqueId"
                placeholder="请输入抖音号"
                class="w-full sm:w-64 md:w-56 lg:w-64"
                clearable
              />
              <el-button type="primary" icon="search" @click="onSubmit">查询</el-button>
              <el-button type="success" icon="Refresh" @click="checkAllUsersStatus">刷新</el-button>
              <el-button type="danger" icon="Refresh" @click="getInValidLoginUsers">筛选未登录</el-button>
            </div>
          </div>

          <!-- 表格区域 -->
          <el-table
            ref="tableRef"
            :data="tableData"
            border
            stripe
            class="w-full"
            :header-cell-class-name="'bg-gray-50 dark:bg-slate-800'"
            row-key="ID"
          >
            <el-table-column align="center" label="基本信息" width="180">
              <template #default="scope">
                <div class="flex items-center space-x-2">
                  <el-avatar :size="40" :src="getAvatarUrl(scope.row.avatar)" />
                  <div class="flex flex-col items-start overflow-hidden">
                    <span class="font-medium truncate w-full"
                      >{{ scope.row.nickname }}
                      <!-- 如果accountType为2,则显示一个蓝色V标 -->
                      <el-tag v-if="scope.row.accountType === 2" size="mini" type="primary" class="ml-1">V</el-tag>
                    </span>
                    <div class="flex items-center gap-1">
                      <span class="text-gray-500 text-sm truncate">{{ scope.row.uniqueId }}</span>
                      <el-button
                        type="primary"
                        link
                        size="small"
                        @click="copyUniqueId(scope.row.uniqueId)"
                        style="font-size: 12px; padding: 2px 4px; min-height: auto"
                      >
                        复制
                      </el-button>
                    </div>
                  </div>
                </div>
              </template>
            </el-table-column>

            <el-table-column align="center" label="分类" width="120">
              <template #default="scope">
                <el-button
                  link
                  type="primary"
                  @click="handleUpdateCategory(scope.row)"
                  :class="{ 'text-gray-400': !getCategoryExists(scope.row.categoryId) }"
                >
                  {{ getCategoryName(scope.row.categoryId) }}
                </el-button>
              </template>
            </el-table-column>

            <el-table-column align="center" label="数据统计" width="150">
              <template #default="scope">
                <div class="flex flex-col gap-1">
                  <div class="text-sm">
                    <span>作品/粉丝：</span>
                    <span class="text-blue-500"
                      >{{ formatNumber(scope.row.awemeCount) }} / {{ formatNumber(scope.row.followerCount) }}</span
                    >
                  </div>
                  <div class="text-sm">
                    <span>可提现：</span>
                    <span class="text-green-500">¥{{ formatNumber(scope.row.withdrawableAmount / 100) }}</span>
                  </div>
                </div>
              </template>
            </el-table-column>

            <el-table-column align="center" label="销售数据" width="120">
              <template #default="scope">
                <div class="flex flex-col gap-1 text-sm">
                  <div>昨日：¥{{ formatNumber(scope.row.day1TotalAmount / 100) }}</div>
                  <div>七日：¥{{ formatNumber(scope.row.day7TotalAmount / 100) }}</div>
                  <div>总计：¥{{ formatNumber(scope.row.totalAmount / 100) }}</div>
                </div>
              </template>
            </el-table-column>

            <el-table-column align="left" label="账号状态" width="160">
              <template #default="scope">
                <div class="flex flex-col gap-1">
                  <div class="text-sm text-gray-600">
                    <span>录入人员：{{ scope.row.sysUserName }}</span>
                  </div>
                  <div class="flex flex-col gap-1">
                    <div class="text-sm text-gray-600">
                      <span
                        >登录授权：
                        <el-tag size="small" type="success" v-if="scope.row.status === 1">正常</el-tag>
                        <el-tag size="small" type="info" v-else-if="scope.row.status === 2">失效</el-tag>
                        <el-button
                          size="small"
                          type="info"
                          v-else-if="scope.row.status === 3"
                          @click="checkLoginStatus(scope.row)"
                          >超时重试中</el-button
                        >
                        <el-button size="small" type="danger" v-else @click="checkLoginStatus(scope.row)"
                          >未登录</el-button
                        >
                      </span>
                    </div>
                  </div>
                  <!-- 增加"私信授权" -->
                  <div class="text-sm text-gray-600">
                    <span
                      >营销授权：
                      <el-tag size="small" type="info" v-if="scope.row.talkAuthStatus === 0">未授权</el-tag>
                      <el-tag size="small" type="success" v-if="scope.row.talkAuthStatus === 1">正常</el-tag>
                      <el-tag size="small" type="warning" v-else-if="scope.row.talkAuthStatus === 2">已失效</el-tag>
                      <el-tag size="small" type="danger" v-else-if="scope.row.talkAuthStatus === 3">禁用</el-tag>
                    </span>
                  </div>
                  <div class="text-sm text-gray-500">
                    <span
                      >发布设定：
                      <el-tag
                        size="small"
                        :type="
                          scope.row.autoPublishStatus === 1
                            ? 'success'
                            : scope.row.autoPublishStatus === 2
                            ? 'danger'
                            : 'info'
                        "
                      >
                        {{
                          scope.row.autoPublishStatus === 0
                            ? '未配置'
                            : scope.row.autoPublishStatus === 1
                            ? videoCategoryMap && scope.row.videoCategoryId
                              ? videoCategoryMap[scope.row.videoCategoryId]
                              : '未知分类'
                            : scope.row.autoPublishStatus === 2
                            ? '禁用'
                            : '未配置'
                        }}
                      </el-tag>
                    </span>
                  </div>

                  <div class="text-sm text-gray-600">
                    <span
                      >营销设定：
                      <el-tag
                        size="small"
                        :type="
                          scope.row.commentTraceStatus === 1
                            ? 'success'
                            : scope.row.commentTraceStatus === 2
                            ? 'danger'
                            : 'info'
                        "
                      >
                        {{
                          scope.row.commentTraceStatus === 0
                            ? '未配置'
                            : scope.row.commentTraceStatus === 1
                            ? '已配置'
                            : scope.row.commentTraceStatus === 2
                            ? '禁用'
                            : '未知'
                        }}
                      </el-tag>
                    </span>
                  </div>

                  <div>
                    <el-button
                      :type="scope.row.bindIP ? 'success' : 'warning'"
                      size="small"
                      @click="showBindInfoDialog(scope.row)"
                    >
                      {{ scope.row.bindIP ? '绑定信息' : '未绑定' }}
                    </el-button>
                  </div>
                </div>
              </template>
            </el-table-column>

            <el-table-column align="center" label="实名认证" width="150">
              <template #default="scope">
                <div class="flex flex-col gap-1">
                  <div class="text-sm">
                    账号实名:
                    <el-button
                      size="small"
                      type="text"
                      @click="handleRealNameAction(scope.row)"
                      style="padding-left: 0px"
                    >
                      {{ scope.row.realName || '未实名' }}
                    </el-button>
                  </div>
                  <div class="text-sm">
                    绑定手机:
                    <el-button size="small" type="text" @click="handlePhoneAction(scope.row)" style="padding-left: 0px">
                      {{ scope.row.bindPhone || '未绑定' }}
                    </el-button>
                    <!-- 添加运营商和实名信息 -->
                    <div v-if="scope.row.bindPhone" class="text-xs text-gray-500">
                      {{ scope.row.phoneOperator }}-{{ scope.row.phoneUserName || '未实名' }}
                    </div>
                  </div>
                </div>
              </template>
            </el-table-column>

            <el-table-column align="center" label="备注" width="220">
              <template #default="scope">
                <el-tooltip :content="scope.row.remark || '添加备注'" placement="top" :show-after="500">
                  <el-button type="primary" link size="small" @click="showRemarkDialog(scope.row)">
                    <template v-if="scope.row.remark">
                      <div class="overflow-y-auto whitespace-pre-line text-left">
                        {{ scope.row.remark }}
                      </div>
                      <el-icon class="ml-1">
                        <Edit />
                      </el-icon>
                    </template>
                    <el-icon v-else>
                      <Edit />
                    </el-icon>
                  </el-button>
                </el-tooltip>
              </template>
            </el-table-column>

            <!-- 拖拽手柄列 -->
            <el-table-column align="center" label="排序" width="120">
              <template #default="scope">
                <div class="flex flex-col items-center gap-1 sort-buttons">
                  <!-- 上移按钮 -->
                  <el-button
                    size="small"
                    type="primary"
                    :disabled="scope.$index === 0"
                    @click="moveUp(scope.$index)"
                    style="padding: 2px 4px; min-height: auto"
                  >
                    <el-icon size="12">
                      <ArrowUp />
                    </el-icon>
                  </el-button>

                  <!-- 拖拽手柄 -->
                  <div
                    class="drag-handle cursor-move text-gray-400 hover:text-gray-600 flex items-center justify-center"
                    style="padding: 4px"
                  >
                    <el-icon size="14">
                      <Rank />
                    </el-icon>
                  </div>

                  <!-- 下移按钮 -->
                  <el-button
                    size="small"
                    type="primary"
                    :disabled="scope.$index === tableData.length - 1"
                    @click="moveDown(scope.$index)"
                    style="padding: 2px 4px; min-height: auto"
                  >
                    <el-icon size="12">
                      <ArrowDown />
                    </el-icon>
                  </el-button>
                </div>
              </template>
            </el-table-column>

            <el-table-column align="center" label="操作" width="240" fixed="right">
              <template #default="scope">
                <div class="operation-buttons">
                  <div class="button-row">
                    <el-button type="warning" size="small" @click="showAutoPublishDialog(scope.row)">
                      发布设定
                    </el-button>
                    <el-button type="warning" size="small" @click="showContentSettingDialog(scope.row)">
                      营销设定
                    </el-button>
                  </div>
                  <div class="button-row">
                    <el-button type="warning" size="small" @click="singleFlush(scope.row)"> 刷新数据 </el-button>
                    <el-button type="warning" size="small" @click="viewAwemes(scope.row)"> 作品管理 </el-button>
                  </div>
                  <div class="button-row">
                    <el-dropdown>
                      <el-button type="primary" size="small">
                        更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
                      </el-button>
                      <template #dropdown>
                        <el-dropdown-menu>
                          <el-dropdown-item @click="showUserDetailDialog(scope.row)">
                            <span class="text-blue-500">查看详情</span>
                          </el-dropdown-item>
                          <el-dropdown-item
                            divided
                            v-if="scope.row.isProductEnabled"
                            @click="handleDisableProduct(scope.row)"
                          >
                            <span class="text-red-500">关闭选品</span>
                          </el-dropdown-item>
                          <el-dropdown-item v-else @click="handleProductAction(scope.row)">
                            <span class="text-green-500">开始选品</span>
                          </el-dropdown-item>
                          <el-dropdown-item divided @click="deleteUserFunc(scope.row)">
                            <span class="text-red-500">删除</span>
                          </el-dropdown-item>
                        </el-dropdown-menu>
                      </template>
                    </el-dropdown>
                  </div>
                </div>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页区域 -->
          <div class="flex justify-end mt-4">
            <el-pagination
              :current-page="page"
              :page-size="pageSize"
              :page-sizes="[100, 200, 300, 400]"
              :total="total"
              layout="total, sizes, prev, pager, next, jumper"
              @current-change="handleCurrentChange"
              @size-change="handleSizeChange"
              background
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 添加分类弹窗 -->
    <el-dialog
      v-model="categoryDialogVisible"
      @close="closeAddCategoryDialog"
      width="520"
      :title="(categoryFormData.ID === 0 ? '添加' : '编辑') + '分类'"
      draggable
    >
      <el-form ref="categoryForm" :rules="rules" :model="categoryFormData" label-width="80px">
        <el-form-item label="上级分类">
          <el-tree-select
            v-model="categoryFormData.pid"
            :data="categories"
            check-strictly
            :props="defaultProps"
            :render-after-expand="false"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="分类名称" prop="name">
          <el-input v-model.trim="categoryFormData.name" placeholder="分类名称"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="closeAddCategoryDialog">取消</el-button>
        <el-button type="primary" @click="confirmAddCategory">确定</el-button>
      </template>
    </el-dialog>

    <!-- 抖音号搜索弹窗 -->
    <el-dialog v-model="searchUniqueIdDialogVisible" title="搜索抖音号" width="520px" center draggable>
      <div class="flex flex-col items-center gap-4">
        <el-form :model="authForm" label-width="80px" class="w-full">
          <el-form-item label="账号类型">
            <el-radio-group v-model="searchUniqueIdForm.accountType">
              <el-radio :label="1">个人账号</el-radio>
              <el-radio :label="2">企业账号</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="选择分类">
            <el-tree-select
              v-model="searchUniqueIdForm.categoryId"
              :data="categories"
              check-strictly
              :props="defaultProps"
              :render-after-expand="false"
              style="width: 100%"
              placeholder="请选择分类"
            />
          </el-form-item>
          <el-form-item label="抖音号">
            <el-input v-model="searchUniqueIdForm.uniqueId" placeholder="请输入抖音号" />
          </el-form-item>
          <!-- 增加一列代理ip选项 -->
          <el-form-item label="代理IP">
            <el-input v-model="searchUniqueIdForm.proxy" placeholder="请获取代理IP" disabled />
            <!-- 增加两个文字按钮：1.搜索匹配 2.独立ip -->
            <el-button type="text" @click="searchProxyFit" style="font-size: 12px; text-decoration: underline"
              >搜索匹配同手机ip</el-button
            >
            <el-button type="text" @click="getIndependentIp" style="font-size: 12px; text-decoration: underline"
              >独立IP</el-button
            >
          </el-form-item>

          <el-form-item label="匹配代理" v-if="searchProxyFormItem">
            <!-- 在同一行增加两个元素，一个是下拉框fitProxyChannel：1-抖音号，2-代理端口。另一个是el-autocomplete组件，用于搜索匹配的代理 -->
            <el-col :span="6">
              <el-select v-model="fitProxyChannel" placeholder="请选择渠道">
                <el-option label="抖音号" value="1"></el-option>
                <el-option label="代理端口" value="2"></el-option>
                <el-option label="手机MAC地址" value="3"></el-option>
              </el-select>
            </el-col>
            <el-col :span="16">
              <el-autocomplete
                v-model="fitProxyKeyword"
                :fetch-suggestions="fitProxySearch"
                placeholder="请根据渠道输入关键信息"
                @select="handleFitProxySelect"
                :trigger-on-focus="true"
                popper-class="phone-suggestions"
                class="w-full"
              >
                <template #default="{ item }">
                  {{
                    Number(fitProxyChannel) === 1
                      ? item.nickname
                      : Number(fitProxyChannel) === 2
                      ? item.bindIP
                      : item.nickname || item.bindIP + ' (匹配MAC)'
                  }}
                </template>
              </el-autocomplete>
            </el-col>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <el-button type="primary" @click="authGetQrCode">前往授权</el-button>
        <el-button @click="closeSearchUniqueIdDialog">关闭</el-button>
      </template>
    </el-dialog>

    <!-- 新添加授权弹窗 -->
    <el-dialog
      v-model="getQrCodeDialogVisible"
      title="获取二维码"
      width="520px"
      center
      draggable
      @open="startPolling"
      @close="stopPolling"
    >
      <div class="flex flex-col items-center gap-4">
        <el-form :model="authForm" label-width="80px" class="w-full">
          <el-form-item label="uniqueId">
            <el-input v-model="authForm.uniqueId" disabled />
          </el-form-item>
          <el-form-item label="选择分类" disabled>
            <el-tree-select
              v-model="authForm.categoryId"
              :data="categories"
              check-strictly
              :props="defaultProps"
              :render-after-expand="false"
              style="width: 100%"
              placeholder="请选择分类"
            />
          </el-form-item>
          <el-form-item label="did">
            <el-input v-model="authForm.did" placeholder="请输入设备ID" disabled />
          </el-form-item>
          <el-form-item label="iid">
            <el-input v-model="authForm.iid" placeholder="请输入iid" disabled />
          </el-form-item>
          <el-form-item label="代理">
            <el-input v-model="authForm.proxy" placeholder="请输入代理" disabled />
          </el-form-item>
          <el-form-item label="扫码登录">
            <!-- 显示二维码图片 -->
            <img
              v-if="authForm.qr_code_url"
              :src="`https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=${encodeURIComponent(
                authForm.qr_code_url
              )}`"
              alt="二维码"
            />
            <el-input v-model="authForm.qr_code_url" disabled v-else />
          </el-form-item>
          <div v-if="authForm.warningText" class="warning-text" style="color: orange">{{ authForm.warningText }}</div>
          <!-- 添加v-if指令来控制显示隐藏 -->
          <el-form-item label="验证码" v-if="authForm.showCaptchaInput">
            <el-input v-model="authForm.code" placeholder="请输入验证码" />
          </el-form-item>
          <el-form-item v-if="authForm.showCaptchaInput">
            <el-button @click="validSms">验证</el-button>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <el-button @click="closeGetQrCodeDialog">关闭</el-button>
      </template>
    </el-dialog>

    <!-- 更新IP弹窗 -->
    <el-dialog v-model="updateIPDialogVisible" title="更新绑定IP" width="400px" center draggable>
      <el-form :model="updateIPForm" label-width="80px">
        <el-form-item label="用户">
          <span>{{ updateIPForm.nickname }}</span>
        </el-form-item>
        <el-form-item label="当前IP">
          <el-tag size="small" type="info" v-if="!updateIPForm.currentIP">未绑定</el-tag>
          <span v-else>{{ updateIPForm.currentIP }}</span>
        </el-form-item>
        <el-form-item label="新IP" prop="newIP">
          <div class="flex gap-2 items-start">
            <el-select
              v-model="updateIPForm.newIP"
              placeholder="请选择或搜索新的IP地址"
              class="flex-1"
              filterable
              clearable
            >
              <el-option v-for="ip in updateIPForm.availableIPs" :key="ip" :label="ip" :value="ip" />
            </el-select>
            <el-button type="warning" size="default" @click="clearIPInput" :disabled="!updateIPForm.newIP">
              清空
            </el-button>
          </div>
          <div class="text-gray-500 text-sm mt-1">
            {{ updateIPForm.newIP ? '请选择新的IP地址' : '清空IP后确定将解绑当前IP' }}
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="updateIPDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmUpdateIP">
          {{ updateIPForm.newIP ? '更新' : '解绑' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 实名弹窗 -->
    <el-dialog v-model="bindRealNameDialogVisible" title="实名认证" width="400px" center draggable>
      <el-form :model="bindRealNameForm" label-width="80px">
        <el-form-item label="用户">
          <span>{{ bindRealNameForm.nickname }}</span>
        </el-form-item>
        <el-form-item label="姓名">
          <div class="flex gap-2 items-start">
            <el-input v-model="bindRealNameForm.realName" placeholder="请输入真实姓名，留空表示清空" class="flex-1" />
            <el-button type="warning" size="default" @click="clearRealNameInput" :disabled="!bindRealNameForm.realName">
              清空
            </el-button>
          </div>
          <div class="text-gray-500 text-sm mt-1">请输入真实姓名，或点击清空按钮清除当前实名认证</div>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="bindRealNameDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmBindRealName">确定</el-button>
      </template>
    </el-dialog>

    <!-- 绑定设备弹窗 -->
    <el-dialog v-model="bindPhoneDialogVisible" title="绑定手机号" width="400px" center draggable>
      <el-form :model="bindPhoneForm" label-width="80px">
        <el-form-item label="用户">
          <span>{{ bindPhoneForm.nickname }}</span>
        </el-form-item>
        <el-form-item label="手机号">
          <div class="flex gap-2 items-start">
            <el-autocomplete
              v-model="bindPhoneForm.phone"
              :fetch-suggestions="phoneSearch"
              placeholder="请输入手机号搜索，留空表示解绑"
              @select="phoneSelected"
              :trigger-on-focus="true"
              popper-class="phone-suggestions"
              class="flex-1"
            >
              <template #default="{ item }">
                <div class="flex flex-col">
                  <span>{{ item.phoneNumber }}</span>
                  <span class="text-gray-500 text-sm"
                    >{{ item.operatorType === 'mobile' ? '移动' : item.operatorType === 'unicom' ? '联通' : '电信' }} -
                    {{ item.realName || '未实名' }}</span
                  >
                </div>
              </template>
            </el-autocomplete>
            <el-button type="warning" size="default" @click="clearPhoneInput" :disabled="!bindPhoneForm.phone">
              清空
            </el-button>
          </div>
          <div class="text-gray-500 text-sm mt-1">
            {{ bindPhoneForm.phone ? '请从下拉列表中选择手机号' : '清空手机号后确定将解绑当前手机号' }}
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="bindPhoneDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmBindPhone">
          {{ bindPhoneForm.phone ? '绑定' : '解绑' }}
        </el-button>
      </template>
    </el-dialog>
    <!-- 自动发布设置弹窗 -->
    <el-dialog
      v-model="autoPublishDialogVisible"
      title="发布设定"
      width="800px"
      @close="closeAutoPublishDialog"
      center
      draggable
    >
      <!-- 增加"日期模板" -->
      <div style="margin-top: 5px">
        <div class="flex items-center gap-2">
          <div class="section-title">日期模板：</div>
          <div class="flex flex-wrap gap-2">
            <span
              v-for="template in publishTemplateNameList"
              :key="template.templateId"
              class="px-2 py-1 bg-blue-200 rounded flex items-center gap-2"
            >
              <span @click="chooseTemplate(template)">
                {{ template.templateName }}
              </span>
              <el-icon class="cursor-pointer mr-2" style="font-size: 14px" @click="editTemplate(template)">
                <Edit />
              </el-icon>
              <el-icon class="cursor-pointer" style="font-size: 14px" @click="deleteTemplate(template)">
                <Delete />
              </el-icon>
            </span>
          </div>
          <el-icon @click="showAutoPublishTemplateDialog"><CirclePlus /></el-icon>
        </div>
      </div>
      <div style="margin-top: 5px">
        <span class="section-title"
          >选择日期：
          <el-link type="danger" size="mini" @click="clearPublishData">
            <el-icon> <Delete /> </el-icon>清空
          </el-link>
          <el-link type="primary" size="mini" @click="handleResetPublishData" class="ml-2">
            <el-icon> <Refresh /> </el-icon>默认
          </el-link>
        </span>
      </div>
      <div style="margin-top: 5px">
        <el-tabs v-model="publishCurrentDay" type="border-card">
          <el-tab-pane label="周一" :name="1"></el-tab-pane>
          <el-tab-pane label="周二" :name="2"></el-tab-pane>
          <el-tab-pane label="周三" :name="3"></el-tab-pane>
          <el-tab-pane label="周四" :name="4"></el-tab-pane>
          <el-tab-pane label="周五" :name="5"></el-tab-pane>
          <el-tab-pane label="周六" :name="6"></el-tab-pane>
          <el-tab-pane label="周日" :name="7"></el-tab-pane>
          <div class="section-title">选择时段：</div>
          <el-checkbox-group v-model="currentPublishData[publishCurrentDay]">
            <el-checkbox :label="0">0点</el-checkbox>
            <el-checkbox :label="1">1点</el-checkbox>
            <el-checkbox :label="2">2点</el-checkbox>
            <el-checkbox :label="3">3点</el-checkbox>
            <el-checkbox :label="4">4点</el-checkbox>
            <el-checkbox :label="5">5点</el-checkbox>
            <el-checkbox :label="6">6点</el-checkbox>
            <el-checkbox :label="7">7点</el-checkbox>
            <el-checkbox :label="8">8点</el-checkbox>
            <el-checkbox :label="9">9点</el-checkbox>
            <el-checkbox :label="10">10点</el-checkbox>
            <el-checkbox :label="11">11点</el-checkbox>
            <el-checkbox :label="12">12点</el-checkbox>
            <el-checkbox :label="13">13点</el-checkbox>
            <el-checkbox :label="14">14点</el-checkbox>
            <el-checkbox :label="15">15点</el-checkbox>
            <el-checkbox :label="16">16点</el-checkbox>
            <el-checkbox :label="17">17点</el-checkbox>
            <el-checkbox :label="18">18点</el-checkbox>
            <el-checkbox :label="19">19点</el-checkbox>
            <el-checkbox :label="20">20点</el-checkbox>
            <el-checkbox :label="21">21点</el-checkbox>
            <el-checkbox :label="22">22点</el-checkbox>
            <el-checkbox :label="23">23点</el-checkbox>
          </el-checkbox-group>
        </el-tabs>
      </div>
      <div style="margin-top: 5px">
        <span class="section-title">选择类型：</span>
        <el-radio-group v-model="publishCurrentType">
          <el-radio :label="1">视频</el-radio>
          <el-radio :label="2">图文</el-radio>
        </el-radio-group>
      </div>
      <div style="margin-top: 5px">
        <span>选择类库：</span>
        <el-radio-group v-model="publishCurrentVideoCategory">
          <el-radio v-for="category in videoCategoryList" :key="category.ID" :label="category.ID">
            {{ category.name }}
          </el-radio>
        </el-radio-group>
      </div>
      <div style="margin-top: 5px">
        <span>当前状态：</span>
        <span v-if="currentPublishUser?.autoPublishStatus === 0" class="text-gray-500"
          ><el-icon>
            <CircleClose />
          </el-icon>
          未配置</span
        >
        <span v-else-if="currentPublishUser?.autoPublishStatus === 1" class="text-green-500"
          ><el-icon>
            <CircleCheck />
          </el-icon>
          已配置</span
        >
        <span v-else-if="currentPublishUser?.autoPublishStatus === 2" class="text-red-500"
          ><el-icon>
            <Warning />
          </el-icon>
          禁用</span
        >
      </div>
      <div style="margin-top: 5px">
        <span>状态设置：</span>
        <el-button-group v-if="currentPublishUser?.autoPublishStatus === 1">
          <el-button
            size="small"
            type="warning"
            @click="
              ElMessageBox.confirm('确定要禁用该账号的自动发布功能吗?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
              }).then(() => handleChangeAutoPublishStatus(2))
            "
            style="margin-right: 1px"
            >禁用</el-button
          >
          <el-button
            size="small"
            type="danger"
            @click="
              ElMessageBox.confirm('确定要删除该账号的自动发布配置吗?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
              }).then(() => handleChangeAutoPublishStatus(3))
            "
            style="margin-left: 1px"
            >删除</el-button
          >
        </el-button-group>
        <el-button-group v-else-if="currentPublishUser?.autoPublishStatus === 2">
          <el-button
            size="small"
            type="success"
            @click="
              ElMessageBox.confirm('确定要启用该账号的自动发布功能吗?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
              }).then(() => handleChangeAutoPublishStatus(1))
            "
            style="margin-right: 1px"
            >启用</el-button
          >
          <el-button
            size="small"
            type="danger"
            @click="
              ElMessageBox.confirm('确定要删除该账号的自动发布配置吗?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
              }).then(() => handleChangeAutoPublishStatus(3))
            "
            style="margin-left: 1px"
            >删除</el-button
          >
        </el-button-group>
      </div>
      <template #footer>
        <el-button @click="closeAutoPublishDialog">取消</el-button>
        <el-button type="primary" @click="handleAutoPublish">确定</el-button>
      </template>
    </el-dialog>

    <!-- 设置发布模板弹窗 -->
    <el-dialog
      v-model="autoPublishTemplateDialogVisible"
      title="发布时间模板"
      width="800px"
      @close="closeAutoPublishTemplateDialog"
      center
      draggable
    >
      <!-- 增加一列输入框："模板名称" -->
      <div style="margin-top: 5px">
        <div class="flex items-center gap-2">
          <div class="section-title">模板名称：</div>
          <el-input v-model="publishTemplateCurrentName" placeholder="请输入模板名称" style="width: 200px" />
        </div>
      </div>
      <div style="margin-top: 5px">
        <span class="section-title"
          >选择日期：
          <el-link type="danger" size="mini" @click="clearPublishTemplateData">
            <el-icon> <Delete /> </el-icon>清空
          </el-link>
        </span>
      </div>
      <div style="margin-top: 5px">
        <el-tabs v-model="publishTemplateCurrentDay" type="border-card">
          <el-tab-pane label="周一" :name="1"></el-tab-pane>
          <el-tab-pane label="周二" :name="2"></el-tab-pane>
          <el-tab-pane label="周三" :name="3"></el-tab-pane>
          <el-tab-pane label="周四" :name="4"></el-tab-pane>
          <el-tab-pane label="周五" :name="5"></el-tab-pane>
          <el-tab-pane label="周六" :name="6"></el-tab-pane>
          <el-tab-pane label="周日" :name="7"></el-tab-pane>
          <div class="section-title">选择时段：</div>
          <el-checkbox-group v-model="currentPublishTemplateData[publishTemplateCurrentDay]">
            <el-checkbox :label="0">0点</el-checkbox>
            <el-checkbox :label="1">1点</el-checkbox>
            <el-checkbox :label="2">2点</el-checkbox>
            <el-checkbox :label="3">3点</el-checkbox>
            <el-checkbox :label="4">4点</el-checkbox>
            <el-checkbox :label="5">5点</el-checkbox>
            <el-checkbox :label="6">6点</el-checkbox>
            <el-checkbox :label="7">7点</el-checkbox>
            <el-checkbox :label="8">8点</el-checkbox>
            <el-checkbox :label="9">9点</el-checkbox>
            <el-checkbox :label="10">10点</el-checkbox>
            <el-checkbox :label="11">11点</el-checkbox>
            <el-checkbox :label="12">12点</el-checkbox>
            <el-checkbox :label="13">13点</el-checkbox>
            <el-checkbox :label="14">14点</el-checkbox>
            <el-checkbox :label="15">15点</el-checkbox>
            <el-checkbox :label="16">16点</el-checkbox>
            <el-checkbox :label="17">17点</el-checkbox>
            <el-checkbox :label="18">18点</el-checkbox>
            <el-checkbox :label="19">19点</el-checkbox>
            <el-checkbox :label="20">20点</el-checkbox>
            <el-checkbox :label="21">21点</el-checkbox>
            <el-checkbox :label="22">22点</el-checkbox>
            <el-checkbox :label="23">23点</el-checkbox>
          </el-checkbox-group>
        </el-tabs>
      </div>
      <template #footer>
        <el-button @click="closeAutoPublishTemplateDialog">取消</el-button>
        <el-button type="primary" @click="handleAutoPublishTemplate">确定</el-button>
      </template>
    </el-dialog>

    <!-- 更新分类弹窗 -->
    <el-dialog v-model="updateCategoryDialogVisible" title="更新分类" width="400px" center draggable>
      <el-form :model="categoryFormData">
        <el-form-item label="选择分类">
          <el-select v-model="categoryFormData.categoryId" placeholder="请选择分类" style="width: 100%">
            <el-option
              v-for="category in flattenCategories"
              :key="category.ID"
              :label="category.name"
              :value="category.ID"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="updateCategoryDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmUpdateCategory">确定</el-button>
      </template>
    </el-dialog>

    <!-- 添加绑定信息弹窗 -->
    <el-dialog v-model="bindInfoDialogVisible" title="绑定信息" width="800px" center draggable>
      <div class="bind-info-container">
        <!-- 基础信息模块 -->
        <el-card shadow="hover" class="info-section mb-4">
          <template #header>
            <div class="section-header">
              <h4>基础信息</h4>
            </div>
          </template>
          <div class="info-content">
            <!-- 序号/WIFI信息 -->
            <div class="info-item-compact">
              <span class="info-label">序号/WIFI:</span>
              <span class="info-value">{{ currentBindInfo.ipSort || '未知' }}</span>
            </div>

            <!-- 代理IP信息 -->
            <div class="info-item-compact">
              <span class="info-label">代理IP:</span>
              <div class="flex items-center gap-2 flex-1">
                <span class="ip-address">{{ currentBindInfo.bindIP || '未绑定' }}</span>
                <el-tag
                  v-if="currentBindInfo.bindIP && currentBindInfo.ipHealthStatus"
                  :type="getIPHealthStatusType(currentBindInfo.ipHealthStatus)"
                  size="small"
                >
                  {{ getIPHealthStatusText(currentBindInfo.ipHealthStatus) }}
                </el-tag>
                <!-- 操作按钮直接放在IP旁边 -->
                <div class="flex gap-1 ml-auto">
                  <el-button size="small" type="primary" @click="showUpdateIPDialog(currentBindInfo)">
                    {{ currentBindInfo.bindIP ? '更换' : '绑定' }}
                  </el-button>
                  <el-button
                    v-if="currentBindInfo.bindIP"
                    size="small"
                    type="success"
                    @click="checkUserIPHealth(currentBindInfo)"
                  >
                    检查健康
                  </el-button>
                </div>
              </div>
            </div>

            <!-- 设备信息 -->
            <div class="info-item-compact">
              <span class="info-label">设备ID (DID):</span>
              <span class="info-value">{{ currentBindInfo.did || '未配置' }}</span>
            </div>
            <div class="info-item-compact">
              <span class="info-label">实例ID (IID):</span>
              <span class="info-value">{{ currentBindInfo.iid || '未配置' }}</span>
            </div>
          </div>
        </el-card>

        <!-- 同IP用户列表 -->
        <el-card shadow="hover" class="info-section" v-if="currentBindInfo.bindIP">
          <template #header>
            <div class="section-header">
              <h4>同IP用户列表</h4>
              <el-button size="small" type="primary" @click="refreshSameIPUsers" :loading="sameIPUsersLoading">
                刷新
              </el-button>
            </div>
          </template>
          <div class="same-ip-users">
            <el-tabs v-model="activeUserTab">
              <el-tab-pane label="抖音用户" name="douyin">
                <el-table :data="sameIPUsers.dyUsers" border size="small" v-if="sameIPUsers.dyUsers.length > 0">
                  <el-table-column label="昵称" min-width="100">
                    <template #default="scope">
                      <div class="flex flex-col">
                        <div class="font-medium">{{ scope.row.nickname }}</div>
                        <div class="text-sm text-gray-500">{{ scope.row.bindPhone || '未绑定' }}</div>
                        <div v-if="scope.row.bindPhone" class="text-xs text-gray-400">
                          {{ scope.row.phoneOperator }}-{{ scope.row.phoneUserName || '未实名' }}
                        </div>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="uniqueId" label="抖音号" min-width="120" />
                  <el-table-column label="备注" min-width="150">
                    <template #default="scope">
                      <div class="whitespace-pre-line text-sm">
                        {{ scope.row.remark || '无备注' }}
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
                <el-empty v-else description="暂无抖音用户" :image-size="80" />
              </el-tab-pane>
              <el-tab-pane label="火山版用户" name="flame">
                <el-table :data="sameIPUsers.flameUsers" border size="small" v-if="sameIPUsers.flameUsers.length > 0">
                  <el-table-column label="昵称" min-width="100">
                    <template #default="scope">
                      <div class="flex flex-col">
                        <div class="font-medium">{{ scope.row.nickname }}</div>
                        <div class="text-sm text-gray-500">{{ scope.row.bindPhone || '未绑定' }}</div>
                        <div v-if="scope.row.bindPhone" class="text-xs text-gray-400">
                          {{ scope.row.phoneOperator }}-{{ scope.row.phoneUserName || '未实名' }}
                        </div>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="isCollectEnabled" label="采集状态" min-width="100">
                    <template #default="scope">
                      <el-tag :type="scope.row.isCollectEnabled ? 'success' : 'info'" size="small">
                        {{ scope.row.isCollectEnabled ? '已开启' : '未开启' }}
                      </el-tag>
                    </template>
                  </el-table-column>
                </el-table>
                <el-empty v-else description="暂无火山版用户" :image-size="80" />
              </el-tab-pane>
            </el-tabs>
          </div>
        </el-card>
      </div>
    </el-dialog>

    <!-- 添加备注弹窗 -->
    <el-dialog v-model="remarkDialogVisible" title="账号备注" width="400px" center draggable>
      <div class="p-4">
        <el-form :model="remarkForm" label-width="80px">
          <el-form-item label="账号">
            <span>{{ remarkForm.nickname }}</span>
          </el-form-item>
          <el-form-item label="备注">
            <el-input v-model="remarkForm.remark" type="textarea" :rows="3" placeholder="请输入备注信息" />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <el-button @click="remarkDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmUpdateRemark">确定</el-button>
      </template>
    </el-dialog>

    <!-- 用户详情弹窗 -->
    <el-dialog v-model="userDetailDialogVisible" title="账号详情" :width="dialogWidth" center draggable>
      <div class="user-detail-container">
        <el-row :gutter="20">
          <!-- 账号信息 -->
          <el-col :span="24" class="mb-4">
            <el-card shadow="hover" class="detail-card">
              <template #header>
                <div class="card-header">
                  <h3>账号信息</h3>
                </div>
              </template>
              <el-row :gutter="20">
                <el-col :xs="24" :sm="8" :md="8">
                  <div class="info-item">
                    <div class="avatar-container">
                      <el-avatar :size="80" :src="getAvatarUrl(currentUserDetail.avatar)" />
                      <el-tag v-if="currentUserDetail.accountType === 2" type="primary" class="account-type-tag">
                        企业号
                      </el-tag>
                    </div>
                  </div>
                </el-col>
                <el-col :xs="24" :sm="8" :md="8">
                  <div class="info-item">
                    <label>昵称：</label>
                    <span>{{ currentUserDetail.nickname }}</span>
                  </div>
                  <div class="info-item">
                    <label>抖音号：</label>
                    <span>{{ currentUserDetail.uniqueId }}</span>
                    <el-button link type="primary" @click="copyUniqueId(currentUserDetail.uniqueId)" class="ml-2">
                      复制
                    </el-button>
                  </div>
                  <div class="info-item">
                    <label>分类：</label>
                    <span>{{ getCategoryName(currentUserDetail.categoryId) }}</span>
                  </div>
                  <div class="info-item">
                    <label>录入人员：</label>
                    <span>{{ currentUserDetail.sysUserName || '未知' }}</span>
                  </div>
                </el-col>
                <el-col :xs="24" :sm="8" :md="8">
                  <div class="status-item">
                    <label>登录状态：</label>
                    <el-tag :type="getStatusType(currentUserDetail.status)">
                      {{ getStatusText(currentUserDetail.status) }}
                    </el-tag>
                  </div>
                  <div class="status-item">
                    <label>私信授权：</label>
                    <el-tag :type="getTalkAuthType(currentUserDetail.talkAuthStatus)">
                      {{ getTalkAuthText(currentUserDetail.talkAuthStatus) }}
                    </el-tag>
                  </div>
                  <div class="status-item">
                    <label>发布设定：</label>
                    <el-tag :type="getPublishStatusType(currentUserDetail.autoPublishStatus)">
                      {{ getPublishStatusText(currentUserDetail.autoPublishStatus) }}
                    </el-tag>
                  </div>
                  <div class="status-item">
                    <label>营销设定：</label>
                    <el-tag :type="getTraceStatusType(currentUserDetail.commentTraceStatus)">
                      {{ getTraceStatusText(currentUserDetail.commentTraceStatus) }}
                    </el-tag>
                  </div>
                </el-col>
              </el-row>
            </el-card>
          </el-col>

          <!-- 数据信息 -->
          <el-col :span="24" class="mb-4">
            <el-card shadow="hover" class="detail-card">
              <template #header>
                <div class="card-header">
                  <h3>数据信息</h3>
                </div>
              </template>
              <div class="data-section">
                <el-row :gutter="16">
                  <el-col :xs="24" :sm="8" :md="8">
                    <div class="data-item">
                      <div class="data-label">作品数</div>
                      <div class="data-value primary">{{ formatNumber(currentUserDetail.awemeCount) }}</div>
                    </div>
                  </el-col>
                  <el-col :xs="24" :sm="8" :md="8">
                    <div class="data-item">
                      <div class="data-label">粉丝数</div>
                      <div class="data-value success">{{ formatNumber(currentUserDetail.followerCount) }}</div>
                    </div>
                  </el-col>
                  <el-col :xs="24" :sm="8" :md="8">
                    <div class="data-item">
                      <div class="data-label">可提现</div>
                      <div class="data-value warning">
                        ¥{{ formatNumber(currentUserDetail.withdrawableAmount / 100) }}
                      </div>
                    </div>
                  </el-col>
                </el-row>
                <el-divider />
                <el-row :gutter="16">
                  <el-col :xs="24" :sm="8" :md="8">
                    <div class="sales-item">
                      <div class="sales-label">昨日销售额</div>
                      <div class="sales-value">¥{{ formatNumber(currentUserDetail.day1TotalAmount / 100) }}</div>
                    </div>
                  </el-col>
                  <el-col :xs="24" :sm="8" :md="8">
                    <div class="sales-item">
                      <div class="sales-label">七日销售额</div>
                      <div class="sales-value">¥{{ formatNumber(currentUserDetail.day7TotalAmount / 100) }}</div>
                    </div>
                  </el-col>
                  <el-col :xs="24" :sm="8" :md="8">
                    <div class="sales-item">
                      <div class="sales-label">总销售额</div>
                      <div class="sales-value">¥{{ formatNumber(currentUserDetail.totalAmount / 100) }}</div>
                    </div>
                  </el-col>
                </el-row>
              </div>
            </el-card>
          </el-col>

          <!-- 技术信息 -->
          <el-col :span="24">
            <el-card shadow="hover" class="detail-card">
              <template #header>
                <div class="card-header">
                  <h3>技术信息</h3>
                </div>
              </template>
              <el-row :gutter="16">
                <el-col :xs="24" :sm="12" :md="8">
                  <div class="info-item">
                    <label>代理IP：</label>
                    <div class="flex flex-col gap-1">
                      <div class="flex items-center gap-2">
                        <span>{{ currentUserDetail.bindIP || '未绑定' }}</span>
                        <el-tag
                          v-if="currentUserDetail.bindIP && currentUserDetail.ipHealthStatus"
                          :type="getIPHealthStatusType(currentUserDetail.ipHealthStatus)"
                          size="small"
                        >
                          {{ getIPHealthStatusText(currentUserDetail.ipHealthStatus) }}
                        </el-tag>
                      </div>
                      <el-button
                        v-if="currentUserDetail.bindIP"
                        size="small"
                        type="success"
                        link
                        @click="checkUserIPHealth(currentUserDetail)"
                      >
                        检查健康状态
                      </el-button>
                    </div>
                  </div>
                </el-col>
                <el-col :xs="24" :sm="12" :md="8">
                  <div class="info-item">
                    <label>设备ID：</label>
                    <span>{{ currentUserDetail.did || '未配置' }}</span>
                  </div>
                </el-col>
                <el-col :xs="24" :sm="12" :md="8">
                  <div class="info-item">
                    <label>实例ID：</label>
                    <span>{{ currentUserDetail.iid || '未配置' }}</span>
                  </div>
                </el-col>
                <el-col :xs="24" :sm="12" :md="8">
                  <div class="info-item">
                    <label>实名认证：</label>
                    <span>{{ currentUserDetail.realName || '未实名' }}</span>
                  </div>
                </el-col>
                <el-col :xs="24" :sm="12" :md="8">
                  <div class="info-item">
                    <label>绑定手机：</label>
                    <span>{{ currentUserDetail.bindPhone || '未绑定' }}</span>
                  </div>
                </el-col>
                <el-col :xs="24" :sm="12" :md="8">
                  <div class="info-item">
                    <label>手机实名：</label>
                    <span>{{ currentUserDetail.phoneUserName || '未实名' }}</span>
                  </div>
                </el-col>
              </el-row>
              <div class="info-item mt-3" v-if="currentUserDetail.remark">
                <label>备注：</label>
                <div class="remark-content">{{ currentUserDetail.remark }}</div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </el-dialog>

    <!--营销设定弹窗-->
    <el-dialog v-model="contentSettingDialogVisible" title="营销设定" width="600px" center draggable>
      <el-form :model="contentSettingForm" label-width="120px">
        <!-- 增加文字说明："评论设定：命中关键词->回复->关注->私信" -->
        <div class="bg-emerald-100 text-emerald-700 p-3 rounded mb-4">追踪一级评论：命中关键词->回复->关注->发私信</div>
        <!-- 评论设定部分 -->
        <el-form-item label="评论模板">
          <el-input
            v-model="contentSettingForm.commentTemplates"
            type="textarea"
            :rows="5"
            placeholder="请输入评论模板（一个换行表示一条）"
            style="width: calc(100% - 80px)"
          />
        </el-form-item>
        <el-form-item label="关键词">
          <el-input
            v-model="contentSettingForm.commentKeyword"
            placeholder="多个关键词用逗号分隔"
            style="width: calc(100% - 80px)"
          />
        </el-form-item>

        <el-divider />

        <!-- 新增回复设定部分 -->
        <div class="bg-emerald-100 text-emerald-700 p-3 rounded mb-4">私信授权：关注->发私信</div>
        <el-form-item label="第一条私信">
          <el-button type="primary" @click="handlePromotionLink(contentSettingForm)">获取推广链接</el-button>
          <span class="ml-2 text-sm" :class="contentSettingForm.promotionLink ? 'text-green-500' : 'text-red-500'">
            {{ contentSettingForm.promotionLink ? '已获取' : '未获取' }}
          </span>
        </el-form-item>
        <el-form-item label="第二条私信">
          <el-input
            v-model="contentSettingForm.replyTemplates"
            type="textarea"
            :rows="5"
            placeholder="请输入自动回复模板（一个换行表示一条）"
            style="width: calc(100% - 80px)"
          />
        </el-form-item>
        <el-form-item label="营销授权状态">
          <span v-if="contentSettingForm.talkAuthStatus === 0" class="text-gray-500">
            <el-icon>
              <CircleClose />
            </el-icon>
            未授权
          </span>
          <span v-else-if="contentSettingForm.talkAuthStatus === 1" class="text-green-500">
            <el-icon>
              <CircleCheck />
            </el-icon>
            正常
          </span>
          <span v-else-if="contentSettingForm.talkAuthStatus === 2" class="text-red-500">
            <el-icon>
              <Warning />
            </el-icon>
            授权失效
          </span>
          <span v-else-if="contentSettingForm.talkAuthStatus === 3" class="text-gray-500">
            <el-icon>
              <Close />
            </el-icon>
            禁用
          </span>
          <span v-else class="text-gray-500">
            <el-icon>
              <QuestionFilled />
            </el-icon>
            未知状态
          </span>
        </el-form-item>
        <el-form-item label="授权操作">
          <div class="flex items-center gap-4">
            <div class="flex gap-2">
              <el-button
                v-if="contentSettingForm.talkAuthStatus !== 3"
                type="primary"
                size="small"
                @click="handleTalkAuth(contentSettingForm, 1)"
                :loading="openBrowserLoading"
              >
                前往授权
              </el-button>
              <el-button
                v-if="contentSettingForm.biteBrowserId !== ''"
                type="warning"
                size="small"
                @click="clearTalkAuth(contentSettingForm)"
              >
                清空授权
              </el-button>
              <el-button
                v-if="contentSettingForm.talkAuthStatus === 1"
                type="danger"
                size="small"
                @click="handleTalkAuth(contentSettingForm, 3)"
              >
                禁用
              </el-button>
              <el-button
                v-if="contentSettingForm.talkAuthStatus === 3"
                type="success"
                size="small"
                @click="handleTalkAuth(contentSettingForm, 1)"
                :loading="openBrowserLoading"
              >
                启用
              </el-button>
            </div>
          </div>
        </el-form-item>
        <!-- 增加一列按钮"在比特浏览器登录抖音成功后，请按下此按钮"，当isOpenBrowser为true时显示 -->
        <el-form-item v-if="isOpenBrowser" label="">
          <el-button type="primary" size="small" @click="handlConfirmLoginBite">
            在比特浏览器成功登录抖音后，请按下此按钮
          </el-button>
        </el-form-item>

        <el-divider />
        <div>
          <span>营销状态：</span>
          <span v-if="contentSettingForm?.commentTraceStatus === 0" class="text-gray-500"
            ><el-icon>
              <CircleClose />
            </el-icon>
            未配置</span
          >
          <span v-else-if="contentSettingForm?.commentTraceStatus === 1" class="text-green-500"
            ><el-icon>
              <CircleCheck />
            </el-icon>
            已配置</span
          >
          <span v-else-if="contentSettingForm?.commentTraceStatus === 2" class="text-red-500"
            ><el-icon>
              <Warning />
            </el-icon>
            禁用</span
          >
        </div>
        <div>
          <span>状态设置：</span>
          <el-button-group v-if="contentSettingForm.commentTraceStatus === 1">
            <el-button
              size="small"
              type="warning"
              @click="
                ElMessageBox.confirm('确定要禁用该账号的自动营销功能吗?', '提示', {
                  confirmButtonText: '确定',
                  cancelButtonText: '取消',
                  type: 'warning'
                }).then(() => handleUpdateTrackStatus(2))
              "
              style="margin-right: 1px"
              >禁用</el-button
            >
          </el-button-group>
          <el-button-group v-else-if="contentSettingForm?.commentTraceStatus === 2">
            <el-button
              size="small"
              type="success"
              @click="
                ElMessageBox.confirm('确定要启用该账号的自动营销功能吗?', '提示', {
                  confirmButtonText: '确定',
                  cancelButtonText: '取消',
                  type: 'warning'
                }).then(() => handleUpdateTrackStatus(1))
              "
              style="margin-right: 1px"
              >启用</el-button
            >
          </el-button-group>
        </div>
      </el-form>
      <template #footer>
        <el-button @click="closeContentSettings">取消</el-button>
        <el-button type="primary" @click="saveContentSettings">保存</el-button>
      </template>
    </el-dialog>

    <!-- 作品管理弹窗 -->
    <el-dialog
      v-model="awemeDialogVisible"
      :title="`${selectedUser?.nickname || ''} - 作品管理`"
      width="95%"
      :close-on-click-modal="false"
      :destroy-on-close="true"
      top="2vh"
      class="aweme-dialog"
    >
      <div v-if="selectedUser" class="aweme-content">
        <DyAwemeManager :initial-dy-user-ids="[selectedUser.ID]" />
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
  import { ref, nextTick, onMounted, onUnmounted, computed } from 'vue'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { ArrowUp, ArrowDown, Rank } from '@element-plus/icons-vue'
  import {
    getUserList,
    deleteUser,
    toggleProductEnabled as toggleProductEnabledAPI,
    updateUserIP,
    bindDevice,
    updateCategory,
    updateUserRemark,
    updateCommentSetting,
    updateUserTalkAuthStatus,
    clearUserTalkAuthStatus,
    updateUserBiteBrowserId,
    updateUserPromotionLink,
    updateUserTraceStatus,
    fuzzyMatchUsersByProxyPort,
    fuzzyMatchUsersByUniqueId,
    fuzzyMatchUsersByMac,
    saveUserSort,
    getUserSort
  } from '@/api/douyin/dyUser'
  import { getProductFeed } from '@/api/douyin/dyProduct'
  import { getAvailableIP, getUnusedIP, checkSingleIP, getIPBindings, getIPList } from '@/api/douyin/ip'
  import {
    getQrCode,
    checkQrCode,
    sendCaptcha,
    validCaptcha,
    getUserInfoForMore,
    getUserLoginInfoForMore,
    checkAllUserStatusForMore
  } from '@/api/douyin/douyinForMore'
  import { addCategory, deleteCategory, getCategoryList } from '@/api/dyUserCategory'
  import { findPhone } from '@/api/douyin/phoneBalance'
  import { getVideoCategoryList } from '@/api/creative/videoCategory'
  import {
    getAutoPublishVideoListByDyUserId,
    saveAutoPublishVideo,
    changeAutoPublishVideoStauts
  } from '@/api/creative/autoPublishVideo'
  import WarningBar from '@/components/warningBar/warningBar.vue'
  import DyAwemeManager from '@/components/douyin/DyAwemeManager.vue'
  import {
    createBrowser,
    openBrowser,
    deleteBrowser,
    getBrowserInfo,
    updateBrowserInfo,
    updateBrowserProxy,
    getCookie
  } from '@/api/biteBrowser'
  import { saveDouyinWebCookie } from '@/api/douyin/chat'
  import {
    getPublishVideoTemplateNameList,
    getPublishVideoTemplateList,
    savePublishVideoTemplate,
    deletePublishVideoTemplate
  } from '@/api/creative/autoPublishVideoTemplate'
  import Sortable from 'sortablejs'

  defineOptions({
    name: 'AccountList'
  })

  const page = ref(1)
  const total = ref(0)
  const pageSize = ref(100)
  const search = ref({
    nickname: null,
    uniqueId: null,
    categoryId: 0,
    invalidLogin: null
  })
  const tableData = ref([])
  const tableRef = ref(null)
  const sortableInstance = ref(null)

  //是否成功打开比特浏览器
  const isOpenBrowser = ref(false)
  // 打开比特浏览器loading效果
  const openBrowserLoading = ref(false)
  // 比特浏览器相关信息
  const biteBrowserInfo = ref({
    proxyMethod: 2,
    proxyType: 'http',
    proxyUserName: 'smarwpto',
    proxyPassword: '9a07e0r3',
    url: 'https://www.douyin.com'
  })

  const searchProxyFormItem = ref(false)
  const fitProxyChannel = ref(null)
  const fitProxyKeyword = ref(null)
  const publishTemplateNameList = ref([])

  // 分页
  const handleSizeChange = (val) => {
    pageSize.value = val
    getTableData()
  }

  const handleCurrentChange = (val) => {
    page.value = val
    getTableData()
  }

  const onSubmit = () => {
    page.value = 1
    // 主动查询时清空筛选未登录状态
    search.value.invalidLogin = null
    getTableData()
  }

  const getInValidLoginUsers = () => {
    search.value.invalidLogin = 1
    page.value = 1
    getTableData()
  }

  const videoCategoryList = ref([])
  const videoCategoryMap = ref({})
  const fetchVideoCategoryList = async () => {
    const res = await getVideoCategoryList({
      page: 1,
      pageSize: 1000
    })
    if (res.code === 0) {
      console.log('  获取视频库分类成功：', res.data)
      videoCategoryList.value = res.data.list || []
      videoCategoryMap.value = res.data.list.reduce((memo, r) => {
        memo[r.ID] = r.name
        return memo
      }, {})
    } else {
      ElMessage.error('获取视频库分类失败：', res.msg)
    }
  }

  // 查询
  const getTableData = async () => {
    // 显示视频类库
    await fetchVideoCategoryList()

    const table = await getUserList({
      page: page.value,
      pageSize: pageSize.value,
      ...search.value
    })
    if (table.code === 0) {
      tableData.value = table.data.list
      total.value = table.data.total
      page.value = table.data.page
      pageSize.value = table.data.pageSize
    }

    // 在下一个时钟周期初始化拖拽排序
    await nextTick()
    initSortable()
  }
  getTableData()

  // 获取已保存的排序
  const getSavedSort = async () => {
    try {
      const params = search.value.categoryId ? { categoryId: search.value.categoryId } : {}
      const res = await getUserSort(params)
      return res.data || []
    } catch (error) {
      console.error('获取排序失败:', error)
      return []
    }
  }

  // 初始化拖拽排序
  const initSortable = () => {
    if (!tableRef.value) return

    // 如果已经初始化过，先销毁
    if (sortableInstance.value) {
      sortableInstance.value.destroy()
    }

    const tbody = tableRef.value.$el.querySelector('tbody')
    if (!tbody) return

    sortableInstance.value = Sortable.create(tbody, {
      animation: 150,
      handle: '.drag-handle', // 只能通过拖拽手柄进行拖拽
      onEnd: async (evt) => {
        // 获取新的排序
        const newIndex = evt.newIndex
        const oldIndex = evt.oldIndex

        if (newIndex === oldIndex) return

        // 更新本地数据
        const movedItem = tableData.value.splice(oldIndex, 1)[0]
        tableData.value.splice(newIndex, 0, movedItem)

        // 保存排序到后端
        await saveSort()
      }
    })
  }

  // 保存排序
  const saveSort = async () => {
    try {
      const userIds = tableData.value.map((item) => item.ID)
      const params = search.value.categoryId ? { categoryId: search.value.categoryId } : {}

      await saveUserSort({ userIds }, params)
      ElMessage.success('排序保存成功')
    } catch (error) {
      console.error('保存排序失败:', error)
      ElMessage.error('保存排序失败')
      // 重新获取数据，恢复原始顺序
      getTableData()
    }
  }

  // 上移功能
  const moveUp = async (index) => {
    if (index === 0) return

    // 交换数组中的位置
    const item = tableData.value[index]
    tableData.value.splice(index, 1)
    tableData.value.splice(index - 1, 0, item)

    // 保存排序
    await saveSort()
  }

  // 下移功能
  const moveDown = async (index) => {
    if (index === tableData.value.length - 1) return

    // 交换数组中的位置
    const item = tableData.value[index]
    tableData.value.splice(index, 1)
    tableData.value.splice(index + 1, 0, item)

    // 保存排序
    await saveSort()
  }

  // 获取发布模板名称列表
  const getPushlishTemplateNameList = async () => {
    const res = await getPublishVideoTemplateNameList()
    if (res.code === 0) {
      publishTemplateNameList.value = res.data
    }
  }

  const deleteUserFunc = async (row) => {
    ElMessageBox.confirm('此操作将永久删除该用户, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(async () => {
        const res = await deleteUser(row)
        if (res.code === 0) {
          ElMessage({
            type: 'success',
            message: '删除成功!'
          })
          if (tableData.value.length === 1 && page.value > 1) {
            page.value--
          }
          getTableData()
        }
      })
      .catch(() => {
        ElMessage({
          type: 'info',
          message: '已取消删除'
        })
      })
  }

  const defaultProps = {
    children: 'children',
    label: 'name',
    value: 'ID'
  }

  const categories = ref([])
  const flattenCategories = ref([])

  const flattenCategoryTree = (categories) => {
    const result = []
    const flatten = (items, prefix = '') => {
      items.forEach((item) => {
        if (item.ID !== 0) {
          // 排除"全部分类"
          result.push({
            ID: item.ID,
            name: prefix + item.name
          })
        }
        if (item.children && item.children.length > 0) {
          flatten(item.children, prefix + item.name + ' / ')
        }
      })
    }
    flatten(categories)
    return result
  }

  const fetchCategories = async () => {
    const res = await getCategoryList()
    let data = {
      name: '全部分类',
      ID: 0,
      pid: 0,
      children: []
    }
    if (res.code === 0) {
      categories.value = res.data || []
      categories.value.unshift(data)
      // 生成扁平化的分类列表
      flattenCategories.value = flattenCategoryTree(categories.value)
    }
  }

  const handleNodeClick = (node) => {
    search.value.nickname = null
    search.value.uniqueId = null
    search.value.categoryId = node.ID
    search.value.invalidLogin = null // 切换分类时清空筛选未登录状态
    page.value = 1
    getTableData()
  }

  const categoryDialogVisible = ref(false)
  const categoryFormData = ref({
    ID: 0,
    pid: 0,
    name: ''
  })

  const categoryForm = ref(null)
  const rules = ref({
    name: [
      { required: true, message: '请输入分类名称', trigger: 'blur' },
      { max: 20, message: '最多20位字符', trigger: 'blur' }
    ]
  })

  const addCategoryFun = (category) => {
    categoryDialogVisible.value = true
    categoryFormData.value.ID = 0
    categoryFormData.value.pid = category.ID
  }

  const editCategory = (category) => {
    categoryFormData.value = {
      ID: category.ID,
      pid: category.pid,
      name: category.name
    }
    categoryDialogVisible.value = true
  }

  const deleteCategoryFun = async (id) => {
    const res = await deleteCategory({ id: id })
    if (res.code === 0) {
      ElMessage.success({ type: 'success', message: '删除成功' })
      await fetchCategories()
    }
  }

  const confirmAddCategory = async () => {
    categoryForm.value.validate(async (valid) => {
      if (valid) {
        const res = await addCategory(categoryFormData.value)
        if (res.code === 0) {
          ElMessage({ type: 'success', message: '操作成功' })
          await fetchCategories()
          closeAddCategoryDialog()
        }
      }
    })
  }

  const closeAddCategoryDialog = () => {
    categoryDialogVisible.value = false
    categoryFormData.value = {
      ID: 0,
      pid: 0,
      name: ''
    }
  }

  const authForm = ref({
    categoryId: null,
    did: '',
    accountType: null,
    token: null,
    showCaptchaInput: null,
    iid: null,
    proxy: null,
    qr_code_url: null,
    code: null,
    warningText: null
  })

  const searchUniqueIdDialogVisible = ref(false)
  const searchUniqueIdForm = ref({
    uniqueId: null,
    accountType: null,
    categoryId: null,
    proxy: null
  })

  const getQrCodeDialogVisible = ref(false)

  const fullscreenLoading = ref(false)

  const checkingQrCodeFlag = ref(false)

  // 获取二维码前校验
  const authGetQrCode = async () => {
    if (!searchUniqueIdForm.value.uniqueId) {
      ElMessage.error('请填写抖音号')
      return
    }
    if (!searchUniqueIdForm.value.categoryId && searchUniqueIdForm.value.categoryId != 0) {
      ElMessage.error('请选择分类')
      return
    }
    if (!searchUniqueIdForm.value.accountType && searchUniqueIdForm.value.accountType != 0) {
      ElMessage.error('请选择账号类型')
      return
    }
    if (!searchUniqueIdForm.value.proxy) {
      ElMessage.error('请获取合适的代理')
      return
    }
    const res = await getUserInfoForMore({ uniqueId: searchUniqueIdForm.value.uniqueId })
    if (res.code != 0) {
      ElMessage.error('获取用户资料失败:', res.code)
      return
    }
    if (res.data?.ID) {
      ElMessage.error('该用户已完成授权')
      closeSearchUniqueIdDialog()
      return
    }
    authForm.value.uniqueId = searchUniqueIdForm.value.uniqueId
    authForm.value.categoryId = searchUniqueIdForm.value.categoryId
    authForm.value.accountType = searchUniqueIdForm.value.accountType
    authForm.value.proxy = searchUniqueIdForm.value.proxy

    closeSearchUniqueIdDialog()
    showGetQrCodeDialog()
  }
  const showGetQrCodeDialog = async () => {
    try {
      const response = await getQrCode({
        uniqueId: authForm.value.uniqueId,
        accountType: authForm.value.accountType,
        categoryId: authForm.value.categoryId,
        proxy: authForm.value.proxy
      }) // 调用 getQrCode 方法
      if (response.code === 0) {
        authForm.value.token = response.data.token
        authForm.value.did = response.data.did
        authForm.value.iid = response.data.iid
        authForm.value.proxy = response.data.proxy
        authForm.value.qr_code_url = response.data.qr_code_url
        authForm.value.code = null
        getQrCodeDialogVisible.value = true // 显示授权弹窗
      } else {
        ElMessage.error('获取二维码失败：' + response.code)
      }
    } catch (err) {
      // 请求出错时，弹窗报错
      ElMessage.error('请求出错：' + err.message)
    }
  }

  const showSearchUniqueIdDialog = async () => {
    searchUniqueIdForm.value.uniqueId = null
    searchUniqueIdForm.value.accountType = 1
    searchUniqueIdForm.value.categoryId = null
    searchUniqueIdForm.value.proxy = null
    searchUniqueIdDialogVisible.value = true
  }

  const closeSearchUniqueIdDialog = async () => {
    searchUniqueIdForm.value.uniqueId = null
    searchUniqueIdForm.value.accountType = null
    searchUniqueIdForm.value.categoryId = null
    searchUniqueIdForm.value.proxy = null
    searchUniqueIdDialogVisible.value = false
    searchProxyFormItem.value = false
  }

  const closeGetQrCodeDialog = async () => {
    await stopPolling()
    getQrCodeDialogVisible.value = false
    authForm.value.showCaptchaInput = false
    authForm.value.token = null
    authForm.value.did = null
    authForm.value.iid = null
    authForm.value.proxy = null
    authForm.value.qr_code_url = null
    authForm.value.code = null
    authForm.value.warningText = null
    authForm.value.accountType = null
    authForm.value.categoryId = null
    checkingQrCodeFlag.value = false
  }

  const authQrCode = async () => {
    console.log('checkingQrCodeFlag.value', checkingQrCodeFlag.value)
    if (checkingQrCodeFlag.value) {
      return
    }
    const params = {
      token: authForm.value.token,
      categoryId: authForm.value.categoryId
    }
    if (authForm.value.code) {
      params.code = authForm.value.code
    }
    checkingQrCodeFlag.value = true
    const response = await checkQrCode(params)
    console.log('response111111', response)
    if (response.code === 0) {
      checkingQrCodeFlag.value = false
      if (response.data.status) {
        if (response.data.status == 'new') {
          authForm.value.warningText = '请使用抖音扫码授权'
          return
        } else if (response.data.status == 'expired') {
          authForm.value.warningText = '二维码已过期，请重新获取'
          ElMessage.error('二维码已过期，请重新获取')
          closeGetQrCodeDialog()
          getTableData()
          return
        } else if (response.data.status == 'scanned') {
          authForm.value.warningText = '已扫码，请在抖音界面确认登录'
          return
        } else {
          ElMessage.error('扫码状态异常退出：', response.data.status)
          closeGetQrCodeDialog()
          getTableData()
          return
        }
      }

      // 1.停止轮询操作
      await stopPolling()
      if (response.data && response.data.code == 2046) {
        const sendCaptchaRes = await sendCaptcha({ token: authForm.value.token })
        if (sendCaptchaRes.code === 0) {
          authForm.value.showCaptchaInput = true
          authForm.value.warningText = '短信已发送，前注意查收'
        } else {
          authForm.value.warningText = '发送短信验证码失败'
          ElMessage.error('发送短信验证码失败', sendCaptchaRes.msg)
        }
      } else {
        authForm.value.warningText = '授权成功'
        ElMessage.success('授权成功')
        console.log('authForm.value', authForm.value)
        const res = await getLoginInfo({ uniqueId: authForm.value.uniqueId }, 1, 0)
        if (res.code !== 0) {
          ElMessage.warning('新用户登录失效，请重新登录！')
        }
        closeGetQrCodeDialog()
        getTableData()
      }
    } else {
      ElMessage.error('err:', response.msg)
      if (
        response.msg == '账号注册完成，等待系统自动确认登录状态' ||
        response.msg == '刷新完成，系统将自动确认登录状态'
      ) {
        closeGetQrCodeDialog()
        getTableData()
        return
      }
    }
  }

  const validSms = async () => {
    if (!authForm.value.code && authForm.value.code == '') ElMessage.error('请输入验证码')

    const params = {
      token: authForm.value.token,
      code: authForm.value.code
    }
    const response = await validCaptcha(params)
    if (response.code === 0) {
      ElMessage.success('校验成功,请稍后！')
      await authQrCode()
    } else {
      ElMessage.error('validSms:', response.msg)
    }
  }

  const formatNumber = (num) => {
    if (num >= 10000) {
      return (num / 10000).toFixed(1) + 'w'
    }
    return num
  }

  const getAvatarUrl = (avatarJson) => {
    try {
      if (!avatarJson) return ''
      const avatarData = JSON.parse(avatarJson)
      return avatarData.url_list?.[0] || ''
    } catch (err) {
      console.error('解析头像JSON失败:', err)
      return ''
    }
  }

  const directProductSearch = async (row) => {
    try {
      // 添加确认对话框
      ElMessageBox.confirm('确定要为该账号发送选品请求吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      })
        .then(() => {
          // 用户确认后发送请求，禁用全局loading
          getProductFeed(
            {
              token: row.imToken,
              page: 1,
              pageSize: 100
            },
            {
              donNotShowLoading: true // 禁用全局loading
            }
          )
            .then((res) => {
              if (res.code === 0) {
                // 使用接口返回的提示信息
                ElMessage.success(res.msg || '选品请求已发送，请稍后在【选品中心】-【商品列表】下查看结果')
                console.log('选品请求已发送')
              }
            })
            .catch((err) => {
              console.error('选品数据获取失败:', err.message)
              ElMessage.error('发送选品请求失败：' + err.message)
            })
        })
        .catch(() => {
          ElMessage({
            type: 'info',
            message: '已取消选品请求'
          })
        })
    } catch (err) {
      ElMessage.error('发送选品请求失败：' + err.message)
    }
  }

  // 合并选品按钮功能
  const handleProductAction = async (row) => {
    if (!row.isProductEnabled) {
      // 如果选品未开启，先开启选品
      try {
        // 更新状态
        row.isProductEnabled = true

        const res = await toggleProductEnabledAPI({
          id: row.ID,
          enabled: true
        })

        if (res.code === 0) {
          ElMessage.success(res.msg || '选品已开启')
          // 开启成功后，立即执行选品操作
          directProductSearch(row)
          getTableData() // 刷新列表
        } else {
          // 如果API调用失败，恢复状态
          row.isProductEnabled = false
          ElMessage.error('开启选品失败')
        }
      } catch (err) {
        console.error('更新选品状态失败:', err.message)
        ElMessage.error('更新选品状态失败：' + err.message)
        // 恢复开关状态
        row.isProductEnabled = false
      }
    } else {
      // 如果选品已开启，直接执行选品操作
      directProductSearch(row)
    }
  }

  // 实名
  const handleRealNameAction = (row) => {
    bindRealNameForm.value = {
      id: row.ID,
      nickname: row.nickname,
      realName: row.realName
    }
    bindRealNameDialogVisible.value = true
  }

  // 绑定手机号：handlePhoneAction
  const handlePhoneAction = (row) => {
    bindPhoneForm.value = {
      id: row.ID,
      nickname: row.nickname,
      phone: row.bindPhone || '',
      selectedPhone: null
    }
    bindPhoneDialogVisible.value = true
  }

  // 处理关闭选品
  const handleDisableProduct = async (row) => {
    // 添加确认对话框
    ElMessageBox.confirm('确定要关闭选品功能吗?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(async () => {
        try {
          // 更新状态
          row.isProductEnabled = false

          const res = await toggleProductEnabledAPI({
            id: row.ID,
            enabled: false
          })

          if (res.code === 0) {
            ElMessage.success(res.msg || '选品已关闭')
            getTableData() // 刷新列表
          } else {
            // 如果API调用失败，恢复状态
            row.isProductEnabled = true
            ElMessage.error('关闭选品失败')
          }
        } catch (err) {
          console.error('更新选品状态失败:', err.message)
          ElMessage.error('更新选品状态失败：' + err.message)
          // 恢复开关状态
          row.isProductEnabled = true
        }
      })
      .catch(() => {
        ElMessage({
          type: 'info',
          message: '已取消操作'
        })
      })
  }

  // 更新IP相关
  const updateIPDialogVisible = ref(false)
  const updateIPForm = ref({
    id: null,
    nickname: '',
    currentIP: '',
    newIP: '',
    availableIPs: []
  })

  const showUpdateIPDialog = (row) => {
    updateIPForm.value = {
      id: row.ID,
      nickname: row.nickname,
      currentIP: row.bindIP || '',
      newIP: '',
      availableIPs: []
    }

    // 获取可用IP列表
    getAvailableIP()
      .then((res) => {
        if (res.code === 0) {
          updateIPForm.value.availableIPs = res.data || []
          if (updateIPForm.value.availableIPs.length > 0) {
            updateIPForm.value.newIP = updateIPForm.value.availableIPs[0]
          }
        }
      })
      .catch((err) => {
        console.error('获取可用IP失败:', err)
      })

    updateIPDialogVisible.value = true
    // 关闭绑定信息弹窗
    bindInfoDialogVisible.value = false
  }

  // 清空IP输入
  const clearIPInput = () => {
    updateIPForm.value.newIP = ''
  }

  const confirmUpdateIP = async () => {
    try {
      const res = await updateUserIP({
        id: updateIPForm.value.id,
        bindIP: updateIPForm.value.newIP || '' // 允许传空字符串
      })

      if (res.code === 0) {
        const actionText = updateIPForm.value.newIP ? '更新IP' : '解绑IP'
        ElMessage.success(`${actionText}成功`)
        updateIPDialogVisible.value = false
        // 更新当前绑定信息
        if (currentBindInfo.value && currentBindInfo.value.ID === updateIPForm.value.id) {
          currentBindInfo.value.bindIP = updateIPForm.value.newIP || ''
          // 如果解绑了IP，清空相关信息
          if (!updateIPForm.value.newIP) {
            currentBindInfo.value.ipSort = null
            currentBindInfo.value.ipId = null
            currentBindInfo.value.ipHealthStatus = null
            sameIPUsers.value = { dyUsers: [], flameUsers: [] }
          }
        }
        getTableData() // 刷新列表
      }
    } catch (err) {
      console.error('操作失败:', err.message)
      ElMessage.error('操作失败：' + err.message)
    }
  }

  // 实名相关
  const bindRealNameDialogVisible = ref(false)
  const bindRealNameForm = ref({
    id: null,
    nickname: '',
    realName: ''
  })

  // 绑定设备相关
  const bindPhoneDialogVisible = ref(false)
  const bindPhoneForm = ref({
    id: null,
    nickname: '',
    phone: '',
    selectedPhone: null
  })

  const getLoginInfo = async (row, loginForce = 0, chatForce = 0) => {
    const req = { uniqueId: row.uniqueId, loginForce: loginForce, chatForce: chatForce }
    try {
      return await getUserLoginInfoForMore(req)
    } catch (err) {
      throw new Error(`getLoginInfo异常：uniqueId:${row.uniqueId},err:${err.message}`)
    }
  }

  const checkLoginStatus = async (row) => {
    try {
      const res = await getLoginInfo(row, 1, 0)
      if (res.msg != '成功') {
        ElMessage.warning('用户登录失效，正在重新获取登录二维码...')
        authForm.value.uniqueId = row.uniqueId
        authForm.value.categoryId = row.categoryId
        authForm.value.accountType = row.accountType
        if (res.data && res.data.ID) {
          row.status = res.data.status
        } else {
          row.status = 0
        }
        await showGetQrCodeDialog()
      } else {
        ElMessage.success('刷新成功')
        row.awemeCount = res.data.awemeCount
        row.followerCount = res.data.followerCount
        row.likeCount = res.data.likeCount
        row.status = res.data.status
      }
    } catch (err) {
      console.error('检查登录状态失败:', err.message)
      ElMessage.error('检查登录状态失败：' + err.message)
    }
  }

  const phoneSearch = async (phone, cb) => {
    try {
      if (phone.length < 3) {
        cb([])
        return
      }
      const res = await findPhone({ phoneNumber: phone })
      if (res.code === 0) {
        cb(res.data)
      } else {
        ElMessage.error('模糊查找手机号失败：' + res.msg)
        cb([])
      }
    } catch (err) {
      console.error('模糊查找手机号请求出错：', err.message)
      ElMessage.error('模糊查找手机号请求出错：' + err.message)
      cb([])
    }
  }

  const phoneSelected = (item) => {
    bindPhoneForm.value.phone = item.phoneNumber
    bindPhoneForm.value.selectedPhone = item.phoneNumber
  }

  const confirmBindRealName = async () => {
    try {
      const res = await bindDevice({
        id: bindRealNameForm.value.id,
        realName: bindRealNameForm.value.realName
      })

      if (res.code === 0) {
        ElMessage.success(res.msg || '操作成功')
        bindRealNameDialogVisible.value = false
        getTableData()
      }
    } catch (err) {
      console.error('操作失败:', err.message)
      ElMessage.error('操作失败：' + err.message)
    }
  }

  // 清空手机号输入框
  const clearPhoneInput = () => {
    bindPhoneForm.value.phone = ''
    bindPhoneForm.value.selectedPhone = null
  }

  // 清空实名输入框
  const clearRealNameInput = () => {
    bindRealNameForm.value.realName = ''
  }

  const confirmBindPhone = async () => {
    // 如果手机号为空，表示解绑操作
    if (!bindPhoneForm.value.phone || bindPhoneForm.value.phone.trim() === '') {
      try {
        const res = await bindDevice({
          id: bindPhoneForm.value.id,
          phone: '' // 传空字符串表示解绑
        })

        if (res.code === 0) {
          ElMessage.success('解绑成功')
          bindPhoneDialogVisible.value = false
          getTableData()
        }
      } catch (err) {
        console.error('解绑失败:', err.message)
        ElMessage.error('解绑失败：' + err.message)
      }
      return
    }

    // 如果手动修改了手机号(即当前输入的手机号与选中的手机号不一致)
    if (bindPhoneForm.value.phone !== bindPhoneForm.value.selectedPhone) {
      try {
        // 检查手动输入的手机号是否存在于数据库
        const res = await findPhone({ phoneNumber: bindPhoneForm.value.phone })
        if (res.code === 0 && res.data && res.data.length > 0) {
          // 如果找到了手机号，更新selectedPhone
          bindPhoneForm.value.selectedPhone = bindPhoneForm.value.phone
        } else {
          ElMessage.warning('该手机号不在系统中，请从下拉列表中选择')
          return
        }
      } catch (err) {
        console.error('验证手机号失败:', err.message)
        ElMessage.error('验证手机号失败：' + err.message)
        return
      }
    }

    try {
      const res = await bindDevice({
        id: bindPhoneForm.value.id,
        phone: bindPhoneForm.value.selectedPhone
      })

      if (res.code === 0) {
        ElMessage.success('绑定成功')
        bindPhoneDialogVisible.value = false
        getTableData()
      }
    } catch (err) {
      console.error('绑定失败:', err.message)
      ElMessage.error('绑定失败：' + err.message)
    }
  }

  const singleFlush = async (row) => {
    try {
      const res = await getLoginInfo(row, 1, 1)
      if (res.msg != '成功') {
        ElMessage.error('刷新失败：' + res.msg)
        if (res.data && res.data.ID) {
          row.status = res.data.status
        } else {
          row.status = 0
        }
      } else {
        ElMessage.success('刷新成功')
        getTableData()
        return
      }
    } catch (err) {
      console.error('检查登录状态失败:', err.message)
      ElMessage.error('检查登录状态失败：' + err.message)
    }
  }

  // 检查所有用户状态
  const checkAllUsersStatus = async () => {
    try {
      ElMessage.warning('刷新全部可能需要较长时间，请稍后...')
      const res = await checkAllUserStatusForMore({ categoryId: search.value.categoryId })
      console.log('checkAllUserStatusForMore:', res)
      if (res.code != 0) {
        ElMessage.error('刷新失败：' + res.msg)
      }
      ElMessage.success('刷新完成')
      getTableData()
    } catch (error) {
      console.error('刷新失败:', error)
      ElMessage.error('刷新失败')
    }
  }

  fetchCategories()

  // 自动发布相关
  const autoPublishDialogVisible = ref(false)
  const currentPublishUser = ref(null)
  const publishCurrentDay = ref(0)
  const publishCurrentType = ref(0)
  const publishCurrentVideoCategory = ref(null)
  const currentPublishData = ref({
    1: [],
    2: [],
    3: [],
    4: [],
    5: [],
    6: [],
    7: []
  })

  // 默认发布日期和时间
  const defaultPublishData = {
    1: [7, 11, 17],
    2: [8, 12, 18],
    3: [9, 13, 19],
    4: [7, 11, 20],
    5: [11, 17, 21],
    6: [7, 11, 16, 22],
    7: [7, 11, 16, 20]
  }

  // 重置发布日期
  const handleResetPublishData = () => {
    currentPublishData.value = JSON.parse(JSON.stringify(defaultPublishData))
    publishCurrentDay.value = 1
  }

  // 显示自动发布弹窗
  const showAutoPublishDialog = async (row) => {
    await getPushlishTemplateNameList()
    const res = await getAutoPublishVideoListByDyUserId({
      dyUserId: row.ID
    })
    if (res.code != 0) {
      ElMessage.error('获取自动发布列表失败：', res.msg)
      await closeAutoPublishDialog()
      return
    }
    currentPublishUser.value = row
    autoPublishDialogVisible.value = true
    if (res.data && res.data.length > 0) {
      for (const item of res.data) {
        if (!publishCurrentDay.value) publishCurrentDay.value = item.weekday
        if (!publishCurrentType.value) publishCurrentType.value = item.type
        if (!publishCurrentVideoCategory.value) publishCurrentVideoCategory.value = item.videoCategoryId

        currentPublishData.value[item.weekday].push(item.hour)
      }
    } else {
      currentPublishData.value = JSON.parse(JSON.stringify(defaultPublishData))
    }

    if (!publishCurrentDay.value) publishCurrentDay.value = 1
    if (!publishCurrentType.value) publishCurrentType.value = 1
  }

  const closeAutoPublishDialog = async () => {
    currentPublishUser.value = null
    autoPublishDialogVisible.value = false
    publishCurrentDay.value = 0
    publishCurrentType.value = 0
    publishCurrentVideoCategory.value = null
    currentPublishData.value = {
      1: [],
      2: [],
      3: [],
      4: [],
      5: [],
      6: [],
      7: []
    }
  }

  const handleAutoPublish = async () => {
    const list = []
    if (!publishCurrentVideoCategory.value) {
      ElMessage.error('请选择一个类库')
      return
    }
    for (const [weekday, hours] of Object.entries(currentPublishData.value)) {
      for (const hour of hours) {
        list.push({
          weekday: parseInt(weekday),
          hour: parseInt(hour),
          type: publishCurrentType.value,
          videoCategoryId: publishCurrentVideoCategory.value
        })
      }
    }
    console.log('提交自动发布计划', currentPublishUser.value.ID, list)
    const res = await saveAutoPublishVideo({
      dyUserId: currentPublishUser.value.ID,
      list
    })
    if (res.code === 0) {
      ElMessage.success('自动发布计划设定成功')
      await closeAutoPublishDialog()
      getTableData()
    } else {
      ElMessage.error('自动发布计划设定失败：', res.msg)
    }
  }

  /**
   * 修改发布状态
   */
  const handleChangeAutoPublishStatus = async (opt) => {
    console.log('修改发布状态', currentPublishUser.value.ID, opt)
    const res = await changeAutoPublishVideoStauts({
      opt,
      dyUserId: currentPublishUser.value.ID
    })
    if (res.code == 0) {
      ElMessage.success('操作成功')
    } else {
      ElMessage.error('操作失败')
    }
    getTableData()
    await closeAutoPublishDialog()
  }

  const clearPublishData = () => {
    currentPublishData.value = {
      1: [],
      2: [],
      3: [],
      4: [],
      5: [],
      6: [],
      7: []
    }
  }

  const contentSettingDialogVisible = ref(false)
  const contentSettingForm = ref({
    ID: null,
    commentTemplates: '',
    commentTraceStatus: 0,
    commentKeyword: '',
    talkAuthStatus: 0, // 私信授权状态
    promotionLink: '', // 推广链接
    biteBrowserId: '', // 比特浏览器ID
    proxy: '', // 代理
    uniqueId: '', // 抖音号
    replyTemplates: '' // 回复模板
  })

  // 发布模板相关
  const autoPublishTemplateDialogVisible = ref(false)
  const publishTemplateCurrentDay = ref(0)
  const publishTemplateCurrentId = ref(null)
  const publishTemplateCurrentName = ref(null)
  const currentPublishTemplateData = ref({
    1: [],
    2: [],
    3: [],
    4: [],
    5: [],
    6: [],
    7: []
  })

  // 显示自动发布模板弹窗
  const showAutoPublishTemplateDialog = async (row) => {
    publishTemplateCurrentId.value = row.templateId
    publishTemplateCurrentName.value = row.templateName
    if (publishTemplateCurrentId.value) {
      // 根据模板ID获取模板详情
      const res = await getPublishVideoTemplateList({ templateId: row.templateId })
      if (res.code != 0) {
        ElMessage.error('获取自动发布模板详情失败：', res.msg)
        await closeAutoPublishTemplateDialog()
        return
      }
      if (res.data && res.data.length > 0) {
        for (const item of res.data) {
          if (!publishTemplateCurrentDay.value) publishTemplateCurrentDay.value = item.weekday

          currentPublishTemplateData.value[item.weekday].push(item.hour)
        }
      } else {
        // 如果没有模板数据，使用默认数据
        // currentPublishData.value = JSON.parse(JSON.stringify(defaultPublishData));
      }
    }
    if (!publishTemplateCurrentDay.value) publishTemplateCurrentDay.value = 1

    autoPublishTemplateDialogVisible.value = true
  }

  const closeAutoPublishTemplateDialog = async () => {
    autoPublishTemplateDialogVisible.value = false
    publishTemplateCurrentDay.value = 0
    publishTemplateCurrentId.value = null
    publishTemplateCurrentName.value = null
    currentPublishTemplateData.value = {
      1: [],
      2: [],
      3: [],
      4: [],
      5: [],
      6: [],
      7: []
    }
    await getPushlishTemplateNameList()
  }

  const clearPublishTemplateData = () => {
    currentPublishTemplateData.value = {
      1: [],
      2: [],
      3: [],
      4: [],
      5: [],
      6: [],
      7: []
    }
  }

  const handleAutoPublishTemplate = async () => {
    const list = []
    if (!publishTemplateCurrentName.value) {
      ElMessage.error('请输入模板名称')
      return
    }
    for (const [weekday, hours] of Object.entries(currentPublishTemplateData.value)) {
      for (const hour of hours) {
        list.push({
          weekday: parseInt(weekday),
          hour: parseInt(hour),
          templateName: publishTemplateCurrentName.value
        })
      }
    }

    if (list.length === 0) {
      ElMessage.error('请选择发布时段')
      return
    }

    const data = {
      list
    }
    if (publishTemplateCurrentId.value) {
      data.oldTemplateId = publishTemplateCurrentId.value
    }
    const res = await savePublishVideoTemplate(data)
    if (res.code === 0) {
      ElMessage.success('操作成功')
      await closeAutoPublishTemplateDialog()
    } else {
      ElMessage.error('操作失败', res.msg)
    }
  }

  // 编辑模板
  const editTemplate = async (row) => {
    await showAutoPublishTemplateDialog(row)
  }

  const deleteTemplate = async (row) => {
    // 确认删除
    const confirm = await ElMessageBox.confirm('确认删除该模板吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    if (confirm !== 'confirm') {
      return
    }
    const res = await deletePublishVideoTemplate({ templateId: row.templateId })
    if (res.code === 0) {
      ElMessage.success('删除模板成功')
      await getPushlishTemplateNameList()
    } else {
      ElMessage.error('删除模板失败', res.msg)
    }
  }

  // 选取模板
  const chooseTemplate = async (row) => {
    // 确认选择
    const confirm = await ElMessageBox.confirm('确认选择该模板吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    if (confirm !== 'confirm') {
      return
    }
    const res = await getPublishVideoTemplateList({ templateId: row.templateId })
    if (res.code != 0) {
      ElMessage.error('获取模板详情失败：', res.msg)
      return
    }
    console.log('获取模板详情', res.data)
    if (res.data && res.data.length > 0) {
      let chooseDay = 0
      currentPublishData.value = {}
      for (const item of res.data) {
        if (!chooseDay) chooseDay = item.weekday

        currentPublishData.value[item.weekday] ??= []
        currentPublishData.value[item.weekday].push(item.hour)
      }
      if (!chooseDay) chooseDay = 1
      publishCurrentDay.value = chooseDay
      console.log('选择模板', currentPublishData.value)
    }
  }

  const showContentSettingDialog = (row) => {
    contentSettingForm.value = {
      ...row,
      commentKeyword: row.commentKeyword || '1,2,3,4,5,6,7,8,9'
    }
    contentSettingDialogVisible.value = true
  }

  const closeContentSettings = async () => {
    isOpenBrowser.value = false
    contentSettingForm.value = {
      ID: null,
      commentTemplates: null,
      commentTraceStatus: null,
      commentKeyword: null,
      talkAuthStatus: null,
      promotionLink: null,
      biteBrowserId: null,
      proxy: null,
      uniqueId: null,
      replyTemplates: null
    }
    contentSettingDialogVisible.value = false
    getTableData()
  }

  const saveContentSettings = async () => {
    try {
      // if (contentSettingForm.value.promotionLink.trim() === '') {
      //   ElMessage.warning('请设置私信推广链接')
      //   return
      // }
      // if (contentSettingForm.value.commentTemplates.trim() === '') {
      //   ElMessage.warning('请完善评论模板')
      //   return
      // }
      // if (contentSettingForm.value.commentKeyword === '') {
      //   ElMessage.warning('请输入追踪关键词')
      //   return
      // }

      //
      // 如果首次设置，自动开启
      if (contentSettingForm.value.commentTraceStatus === 0) {
        contentSettingForm.value.commentTraceStatus = 1
      }
      const res = await updateCommentSetting(contentSettingForm.value)
      if (res.code === 0) {
        ElMessage.success('设置保存成功')
        getTableData()
        contentSettingDialogVisible.value = false
      }
    } catch (err) {
      ElMessage.error('保存失败：' + err.message)
    }
  }

  const pollingData = ref({
    pollInterval: null,
    stopPollTimeout: ''
  })
  const startPolling = async () => {
    // await authQrCode();// 立即执行第一次
    pollingData.value.pollInterval = setInterval(authQrCode, 3000)
    pollingData.value.stopPollTimeout = setTimeout(stopPolling, 120000)
  }
  const stopPolling = async () => {
    clearInterval(pollingData.value.pollInterval)
    clearTimeout(pollingData.value.stopPollTimeout)
    checkingQrCodeFlag.value = false
    pollingData.value.pollInterval = null
    pollingData.value.stopPollTimeout = null
  }

  const getCategoryName = (categoryId) => {
    const findCategory = (list) => {
      for (const item of list) {
        if (item.ID === categoryId) {
          return item.name
        }
        if (item.children && item.children.length > 0) {
          const found = findCategory(item.children)
          if (found) return found
        }
      }
      return null
    }
    const name = findCategory(categories.value)
    if (name) {
      return name
    }
    // 如果在当前分类中找不到，说明可能是已删除的分类
    return `已删除的分类(${categoryId})`
  }

  const handleUpdateCategory = (row) => {
    categoryFormData.value = {
      ID: row.ID,
      categoryId: row.categoryId
    }
    updateCategoryDialogVisible.value = true
  }

  const updateCategoryDialogVisible = ref(false)

  const confirmUpdateCategory = async () => {
    try {
      const res = await updateCategory({
        id: categoryFormData.value.ID,
        categoryId: categoryFormData.value.categoryId
      })
      if (res.code === 0) {
        ElMessage.success('更新分类成功')
        updateCategoryDialogVisible.value = false
        getTableData()
      }
    } catch (err) {
      ElMessage.error('更新分类失败：' + err.message)
    }
  }

  const getCategoryExists = (categoryId) => {
    const findCategory = (list) => {
      for (const item of list) {
        if (item.ID === categoryId) {
          return true
        }
        if (item.children && item.children.length > 0) {
          const found = findCategory(item.children)
          if (found) return true
        }
      }
      return false
    }
    return findCategory(categories.value)
  }

  // 添加绑定信息弹窗
  const bindInfoDialogVisible = ref(false)
  const currentBindInfo = ref({
    bindIP: '',
    did: '',
    iid: '',
    ID: null,
    nickname: '',
    ipSort: null,
    ipId: null
  })

  // 同IP用户相关
  const sameIPUsers = ref({
    dyUsers: [],
    flameUsers: []
  })
  const sameIPUsersLoading = ref(false)
  const activeUserTab = ref('douyin')

  const showBindInfoDialog = async (row) => {
    currentBindInfo.value = { ...row }
    bindInfoDialogVisible.value = true

    // 如果有IP绑定，获取IP的序号和同IP用户列表
    if (row.bindIP) {
      await loadIPInfoAndUsers(row.bindIP)
    }
  }

  // 加载IP信息和同IP用户列表
  const loadIPInfoAndUsers = async (ip) => {
    try {
      // 首先获取IP列表来找到对应IP的序号和ID
      const ipListRes = await getIPList({ ip, pageSize: 1 })
      if (ipListRes.code === 0 && ipListRes.data.list.length > 0) {
        const ipInfo = ipListRes.data.list[0]
        currentBindInfo.value.ipSort = ipInfo.sort
        currentBindInfo.value.ipId = ipInfo.ID

        // 获取同IP用户列表
        await refreshSameIPUsers()
      }
    } catch (err) {
      console.error('获取IP信息失败:', err)
    }
  }

  // 刷新同IP用户列表
  const refreshSameIPUsers = async () => {
    if (!currentBindInfo.value.ipId) return

    sameIPUsersLoading.value = true
    try {
      const res = await getIPBindings(currentBindInfo.value.ipId)
      if (res.code === 0) {
        sameIPUsers.value = {
          dyUsers: res.data.dyUsers || [],
          flameUsers: res.data.flameUsers || []
        }
      }
    } catch (err) {
      console.error('获取同IP用户失败:', err)
      ElMessage.error('获取同IP用户失败')
    } finally {
      sameIPUsersLoading.value = false
    }
  }

  // 备注相关
  const remarkDialogVisible = ref(false)
  const remarkForm = ref({
    id: null,
    nickname: '',
    remark: ''
  })

  const showRemarkDialog = (row) => {
    remarkForm.value = {
      id: row.ID,
      nickname: row.nickname,
      remark: row.remark || ''
    }
    remarkDialogVisible.value = true
  }

  const confirmUpdateRemark = async () => {
    try {
      const res = await updateUserRemark({
        id: remarkForm.value.id,
        remark: remarkForm.value.remark
      })

      if (res.code === 0) {
        ElMessage.success('更新备注成功')
        remarkDialogVisible.value = false
        getTableData() // 刷新列表
      }
    } catch (err) {
      console.error('更新备注失败:', err.message)
      ElMessage.error('更新备注失败：' + err.message)
    }
  }

  // 营销授权
  const handleTalkAuth = async (row, opt) => {
    try {
      // 当opt为1时，进行授权操作；为3时，进行禁止授权操作
      if (opt === 1) {
        if (!row.biteBrowserId) {
          const [host, port] = row.bindIP.split(':')
          if (!host || !port) {
            ElMessage.error('无效的代理地址')
            return
          }
          // 创建浏览器配置
          const cData = await createBrowser({
            browserFingerPrint: {},
            proxyMethod: biteBrowserInfo.value.proxyMethod,
            proxyType: biteBrowserInfo.value.proxyType,
            host: host,
            port: port,
            proxyUserName: biteBrowserInfo.value.proxyUserName,
            proxyPassword: biteBrowserInfo.value.proxyPassword,
            url: biteBrowserInfo.value.url,
            name: `${row.nickname}(${row.uniqueId})`
          })
          if (!cData) {
            ElMessage.error('创建浏览器失败,请确保比特浏览器已开启')
            return
          }
          if (cData.data?.id) {
            contentSettingForm.value.biteBrowserId = cData.data.id
            // 发起请求,更新用户的浏览器id
            const res = await updateUserBiteBrowserId({
              id: row.ID,
              biteBrowserId: row.biteBrowserId
            })
            if (res.code !== 0) {
              ElMessage.error('更新浏览器ID失败:', res.msg)
              return
            }
          } else {
            ElMessage.error('创建浏览器失败:', cData.msg)
            return
          }
        }
        // 执行浏览器打开操作
        openBrowserLoading.value = true
        ElMessage.warning('正在打开比特浏览器窗口，请稍后')
        await openBiteBrowser(row)
        // 关闭比特浏览器loading效果
        openBrowserLoading.value = false
      } else if (opt === 3) {
        // 发起请求,更新用户的授权状态,不是禁用时，需要等到获取到cookie后，再更新状态
        const res = await updateUserTalkAuthStatus({
          id: row.ID,
          talkAuthStatus: opt
        })
        if (res.code !== 0) {
          ElMessage.error('更新授权状态失败:', res.msg)
          return
        }
        ElMessage.success('操作成功')
        contentSettingForm.value.talkAuthStatus = 3
      } else {
        ElMessage.error('无效操作类型:', opt)
        return
      }
    } catch (err) {
      ElMessage.error(`操作失败: ${err.message}`)
    }
  }

  // 清空营销授权
  const clearTalkAuth = async (row) => {
    // 增加提示框
    const confirm = await ElMessageBox.confirm('确认清空营销授权吗？该操作不可恢复', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    if (confirm !== 'confirm') {
      return
    }

    // 获取浏览器窗口详情
    try {
      const infoRes = await getBrowserInfo({
        id: row.biteBrowserId
      })
      if (!infoRes) {
        ElMessage.error('获取浏览器窗口失败，请确保已打开比特浏览器', res.msg)
        return
      }
    } catch (error) {
      console.error('获取浏览器窗口异常:', error)
      ElMessage.error('打开浏览器异常，请确保已打开比特浏览器')
      return
    }

    // 先修改数据库状态
    const res = await clearUserTalkAuthStatus({
      id: row.ID
    })
    if (res.code !== 0) {
      ElMessage.error('更新授权状态失败:', res.msg)
      return
    }
    const biteRes = await deleteBrowser({
      id: row.biteBrowserId
    })
    console.log('比特浏览器打开结果', biteRes)

    ElMessage.success('已清空营销授权')
    getTableData()
    contentSettingDialogVisible.value = false
    return
  }

  const openBiteBrowser = async (row) => {
    try {
      console.log('用户信息', row)
      // 更新窗口标题，稳定后可以删除
      await updateBrowserInfo({
        ids: [row.biteBrowserId],
        name: `${row.nickname}(${row.uniqueId})`
      })

      const [host, port] = row.bindIP.split(':')
      if (!host || !port) {
        ElMessage.error('无效的代理地址')
        return
      }
      // 更新浏览器代理ip
      await updateBrowserProxy({
        ids: [row.biteBrowserId],
        proxyMethod: biteBrowserInfo.value.proxyMethod,
        proxyType: biteBrowserInfo.value.proxyType,
        host,
        port,
        proxyUserName: biteBrowserInfo.value.proxyUserName,
        proxyPassword: biteBrowserInfo.value.proxyPassword
      })

      const res = await openBrowser({ id: row.biteBrowserId })
      if (!res.success) {
        ElMessage.error('打开浏览器失败：第三方报错：' + res.msg)
        isOpenBrowser.value = false
        return null
      }
      isOpenBrowser.value = true
      return res
    } catch (err) {
      isOpenBrowser.value = false
      ElMessage.error('打开比特浏览器失败：请重启后重试：' + err.message)
      return null
    }
  }

  const handlConfirmLoginBite = async () => {
    try {
      if (!contentSettingForm.value.biteBrowserId) {
        ElMessage.warning('浏览器ID不存在，请先创建浏览器配置')
        return
      }

      const res = await getCookie({
        browserId: contentSettingForm.value.biteBrowserId
      })
      if (!res.success) {
        ElMessage.error('比特浏览器获取Cookie失败:', res.msg)
        return
      }
      let cookie = ''
      let ok = false
      for (const o of res.data) {
        if (o.name == 'sessionid') ok = true
        cookie += `${o.name}=${o.value}; `
      }
      // 对cookie过滤掉前后空格
      cookie = cookie.trim()
      if (!ok) {
        ElMessage.warning('请在比特浏览器登录抖音后再确认')
        return
      }
      // 发起请求saveDouyinWebCookie, 保存cookie
      const sRes = await saveDouyinWebCookie({
        id: contentSettingForm.value.ID,
        cookie: cookie
      })
      if (sRes.code !== 0) {
        ElMessage.error('保存web cookie失败:', sRes.msg)
        return
      }

      const uRes = await updateUserTalkAuthStatus({
        id: contentSettingForm.value.ID,
        talkAuthStatus: 1
      })
      console.log('更新授权状态', uRes)
      if (uRes.code !== 0) {
        ElMessage.error('更新授权状态失败:', uRes.msg)
        return
      }
      contentSettingForm.value.talkAuthStatus = 1
      ElMessage.success('授权成功')
    } catch (err) {
      ElMessage.error('获取Cookie失败:', err)
    }
  }

  // 获取推广链接
  const handlePromotionLink = async (row) => {
    try {
      if (row.promotionLink) {
        ElMessage.success('已设置推广链接')
        return
      }

      // 暂时写死推广链接
      const link = 'https://work.weixin.qq.com/kfid/kfc1ad32a44fefafc3f'
      console.log('请求参数', row.id, link)
      const res = await updateUserPromotionLink({
        id: row.id,
        promotionLink: link
      })
      if (res.code !== 0) {
        ElMessage.error('更新推广链接失败:', res.msg)
        return
      }

      contentSettingForm.value.promotionLink = link
    } catch (err) {
      ElMessage.error('获取推广链接失败:', err.message)
    }
  }

  // 更新用户追踪状态
  const handleUpdateTrackStatus = async (opt) => {
    try {
      const res = await updateUserTraceStatus({
        id: contentSettingForm.value.ID,
        commentTraceStatus: opt
      })
      if (res.code !== 0) {
        ElMessage.error('更新追踪状态失败:', res.msg)
        return
      }

      contentSettingForm.value.commentTraceStatus = opt
    } catch (err) {
      ElMessage.error('更新追踪状态失败:', err.message)
    }
  }

  // 获取独立IP
  const getIndependentIp = async () => {
    try {
      const res = await getUnusedIP()
      if (res.code === 0 && res.data) {
        searchUniqueIdForm.value.proxy = res.data
      }
    } catch (err) {
      console.error('获取独立IP失败:', err)
      ElMessage.error('网络请求失败，请稍后重试')
    }
  }

  const searchProxyFit = () => {
    searchProxyFormItem.value = true
    fitProxyChannel.value = null
    fitProxyKeyword.value = null
  }

  const fitProxySearch = async (queryString, cb) => {
    if (!queryString) {
      return cb([])
    }
    try {
      let response
      if (fitProxyChannel.value == 1) {
        // 抖音号匹配模式
        console.log('抖音号匹配模式')
        response = await fuzzyMatchUsersByUniqueId({ uniqueId: queryString })
      } else if (fitProxyChannel.value == 2) {
        // 代理端口匹配模式
        console.log('代理端口匹配模式')
        response = await fuzzyMatchUsersByProxyPort({ port: queryString })
      } else if (fitProxyChannel.value == 3) {
        // MAC地址匹配模式
        console.log('MAC地址匹配模式')
        response = await fuzzyMatchUsersByMac({ mac: queryString })
      } else {
        return cb([])
      }

      if (response.code === 0) {
        let results
        if (fitProxyChannel.value == 2) {
          // 代理端口搜索返回的是IP池信息
          results = response.data.map((item) => ({
            value: item.ip,
            bindIP: item.ip,
            nickname: `IP: ${item.ip} (城市: ${item.city || '未知'})`,
            ...item
          }))
        } else if (fitProxyChannel.value == 3) {
          // MAC地址搜索返回的是IP池信息
          results = response.data.map((item) => ({
            value: item.ip,
            bindIP: item.ip,
            nickname: `IP: ${item.ip} (城市: ${item.city || '未知'})`,
            ...item
          }))
        } else {
          // 其他搜索返回的是用户信息
          results = response.data.map((item) => ({
            value: fitProxyChannel.value == 1 ? item.nickname : item.bindIP,
            ...item
          }))
        }
        console.log('搜索结果:', results)
        cb(results)
      } else {
        ElMessage.error(response.msg || '搜索失败')
        cb([])
      }
    } catch (e) {
      console.error('搜索异常:', e)
      cb([])
    }
  }

  // 添加选择处理函数
  const handleFitProxySelect = (item) => {
    searchUniqueIdForm.value.proxy = item.bindIP
  }

  // 组件挂载时初始化拖拽排序
  onMounted(async () => {
    await nextTick()
    initSortable()
    // 可以在这里获取已保存的排序，用于验证
    await getSavedSort()
  })

  // 组件卸载时销毁拖拽排序实例
  onUnmounted(() => {
    if (sortableInstance.value) {
      sortableInstance.value.destroy()
    }
  })

  // 用户详情相关
  const userDetailDialogVisible = ref(false)
  const currentUserDetail = ref({})

  // 作品管理弹窗相关变量
  const awemeDialogVisible = ref(false)
  const selectedUser = ref(null)

  // 响应式对话框宽度
  const dialogWidth = computed(() => {
    if (typeof window !== 'undefined') {
      return window.innerWidth <= 768 ? '95%' : '800px'
    }
    return '800px'
  })

  const showUserDetailDialog = (row) => {
    currentUserDetail.value = { ...row }
    userDetailDialogVisible.value = true
  }

  // 状态类型和文本获取方法
  const getStatusType = (status) => {
    switch (status) {
      case 1:
        return 'success'
      case 2:
        return 'info'
      case 3:
        return 'warning'
      default:
        return 'danger'
    }
  }

  const getStatusText = (status) => {
    switch (status) {
      case 1:
        return '正常'
      case 2:
        return '失效'
      case 3:
        return '超时重试中'
      default:
        return '未登录'
    }
  }

  const getTalkAuthType = (status) => {
    switch (status) {
      case 1:
        return 'success'
      case 2:
        return 'warning'
      case 3:
        return 'danger'
      default:
        return 'info'
    }
  }

  const getTalkAuthText = (status) => {
    switch (status) {
      case 1:
        return '正常'
      case 2:
        return '已失效'
      case 3:
        return '禁用'
      default:
        return '未授权'
    }
  }

  const getPublishStatusType = (status) => {
    switch (status) {
      case 1:
        return 'success'
      case 2:
        return 'danger'
      default:
        return 'info'
    }
  }

  const getPublishStatusText = (status) => {
    switch (status) {
      case 1:
        return '已设置'
      case 2:
        return '禁用'
      default:
        return '未配置'
    }
  }

  // 检查用户绑定IP的健康状态
  const checkUserIPHealth = async (user) => {
    if (!user.bindIP) {
      ElMessage.warning('该用户未绑定代理IP')
      return
    }

    try {
      const res = await checkSingleIP({ ip: user.bindIP })
      if (res.code === 0) {
        const result = res.data
        if (result.isHealthy) {
          ElMessage.success(
            `用户 ${user.nickname} 的代理IP ${user.bindIP} 健康检查通过，响应时间: ${result.responseTime}ms`
          )
        } else {
          ElMessage.warning(`用户 ${user.nickname} 的代理IP ${user.bindIP} 健康检查失败: ${result.errorMessage}`)
        }
        // 刷新用户列表以显示最新状态
        getTableData()
      }
    } catch (err) {
      console.error('检查IP健康状态失败:', err)
      ElMessage.error('检查IP健康状态失败')
    }
  }

  // IP健康状态相关工具方法
  const getIPHealthStatusType = (status) => {
    switch (status) {
      case 'healthy':
        return 'success'
      case 'unhealthy':
        return 'danger'
      default:
        return 'info'
    }
  }

  const getIPHealthStatusText = (status) => {
    switch (status) {
      case 'healthy':
        return '健康'
      case 'unhealthy':
        return '不健康'
      default:
        return '未检查'
    }
  }

  const getTraceStatusType = (status) => {
    switch (status) {
      case 1:
        return 'success'
      case 2:
        return 'danger'
      default:
        return 'info'
    }
  }

  const getTraceStatusText = (status) => {
    switch (status) {
      case 1:
        return '已配置'
      case 2:
        return '禁用'
      default:
        return '未配置'
    }
  }

  // 复制抖音号功能
  const copyUniqueId = async (uniqueId) => {
    try {
      await navigator.clipboard.writeText(uniqueId)
      ElMessage.success('抖音号已复制到剪贴板')
    } catch {
      // 如果剪贴板API不可用，使用传统方法
      const textArea = document.createElement('textarea')
      textArea.value = uniqueId
      document.body.appendChild(textArea)
      textArea.select()
      try {
        document.execCommand('copy')
        ElMessage.success('抖音号已复制到剪贴板')
      } catch {
        ElMessage.error('复制失败，请手动复制')
      }
      document.body.removeChild(textArea)
    }
  }

  const viewAwemes = (row) => {
    console.log('查看待发布视频', row.ID)
    // 设置当前选中的用户信息
    selectedUser.value = row
    awemeDialogVisible.value = true
  }
</script>

<style scoped>
  .text-sm {
    font-size: 13px;
    line-height: 1.5;
  }

  .drag-handle {
    padding: 8px;
    border-radius: 4px;
    transition: all 0.2s ease;
  }

  .drag-handle:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }

  .drag-handle:active {
    background-color: rgba(0, 0, 0, 0.1);
  }

  /* 作品管理弹窗样式 */
  .aweme-dialog .el-dialog__body {
    padding: 10px;
    max-height: 85vh;
    overflow-y: auto;
  }

  .aweme-content {
    width: 100%;
    height: 100%;
  }

  /* 移动端优化 */
  @media (max-width: 768px) {
    .drag-handle {
      padding: 8px;
      font-size: 16px;
    }

    /* 移动端排序按钮优化 */
    .sort-buttons .el-button {
      padding: 1px 3px !important;
      min-height: 20px !important;
    }

    .sort-buttons .el-icon {
      font-size: 10px !important;
    }
  }

  /* 用户详情弹窗样式 */
  .user-detail-container {
    max-height: 70vh;
    overflow-y: auto;
  }

  .detail-card {
    margin-bottom: 16px;
  }

  .card-header h3 {
    margin: 0;
    color: #303133;
    font-size: 16px;
    font-weight: 600;
  }

  .avatar-container {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
  }

  .account-type-tag {
    position: absolute;
    top: -5px;
    right: -5px;
    font-size: 10px;
  }

  .info-item {
    margin-bottom: 12px;
  }

  .info-item label {
    font-weight: 600;
    color: #606266;
    margin-right: 8px;
  }

  .status-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
  }

  .status-item {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .status-item label {
    font-weight: 600;
    color: #606266;
    min-width: 80px;
  }

  .data-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }

  .data-item {
    text-align: center;
    padding: 20px;
    border-radius: 12px;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border: 1px solid #e2e8f0;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    margin-bottom: 16px;
  }

  .data-item:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }

  .data-label {
    font-size: 12px;
    color: #909399;
    margin-bottom: 8px;
  }

  .data-value {
    font-size: 18px;
    font-weight: 600;
  }

  .data-value.primary {
    color: #409eff;
  }

  .data-value.success {
    color: #67c23a;
  }

  .data-value.warning {
    color: #e6a23c;
  }

  .data-value.danger {
    color: #f56c6c;
  }

  .sales-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
  }

  .sales-item {
    text-align: center;
    padding: 24px;
    border-radius: 16px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 8px 30px rgba(102, 126, 234, 0.3);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    margin-bottom: 16px;
  }

  .sales-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    pointer-events: none;
  }

  .sales-item:hover {
    transform: translateY(-6px) scale(1.02);
    box-shadow: 0 12px 40px rgba(102, 126, 234, 0.4);
  }

  .sales-label {
    font-size: 14px;
    opacity: 0.9;
    margin-bottom: 12px;
    font-weight: 500;
    position: relative;
    z-index: 1;
  }

  .sales-value {
    font-size: 22px;
    font-weight: 700;
    position: relative;
    z-index: 1;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }

  /* 新的样式定义 */
  .data-section {
    padding: 16px;
    background: rgba(64, 158, 255, 0.02);
    border-radius: 12px;
    border: 1px solid rgba(64, 158, 255, 0.1);
  }

  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #409eff;
    margin: 0 0 16px 0;
    padding-bottom: 8px;
    border-bottom: 2px solid rgba(64, 158, 255, 0.2);
  }

  .mini-data-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
  }

  .mini-sales-grid {
    display: grid;
    grid-template-columns: repeat(1, 1fr);
    gap: 12px;
  }

  /* 绑定信息弹窗样式 */
  .bind-info-container {
    padding: 16px;
    max-height: 75vh;
    overflow-y: auto;
  }

  .info-section {
    border-radius: 12px;
    transition: all 0.3s ease;
  }

  .info-section:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  }

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .section-header h4 {
    margin: 0;
    color: #303133;
    font-size: 16px;
    font-weight: 600;
  }

  .info-content {
    padding: 16px;
  }

  .info-item {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    padding: 8px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #409eff;
  }

  .info-item-compact {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    padding: 6px 8px;
    background: #f8f9fa;
    border-radius: 6px;
    border-left: 3px solid #409eff;
  }

  .info-label {
    font-weight: 600;
    color: #606266;
    min-width: 120px;
    margin-right: 12px;
  }

  .info-value {
    color: #303133;
    font-weight: 500;
  }

  .ip-address {
    font-family: 'Monaco', 'Consolas', monospace;
    background: #f0f2f5;
    padding: 4px 8px;
    border-radius: 4px;
    color: #409eff;
    font-weight: 600;
  }

  .same-ip-users {
    padding: 16px 0;
  }

  .same-ip-users .el-table {
    border-radius: 8px;
    overflow: hidden;
  }

  .same-ip-users .el-table th {
    background: #f8f9fa;
    color: #606266;
    font-weight: 600;
  }

  .same-ip-users .el-empty {
    padding: 40px 20px;
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .bind-info-container {
      padding: 12px;
    }

    .info-item {
      flex-direction: column;
      align-items: flex-start;
    }

    .info-label {
      min-width: auto;
      margin-bottom: 4px;
    }
  }

  .remark-content {
    background: #f5f7fa;
    padding: 12px;
    border-radius: 6px;
    border-left: 4px solid #409eff;
    white-space: pre-wrap;
    margin-top: 8px;
  }

  /* 移动端适配 */
  @media (max-width: 768px) {
    .data-grid {
      grid-template-columns: 1fr;
    }

    .mini-data-grid {
      grid-template-columns: 1fr;
    }

    .mini-sales-grid {
      grid-template-columns: 1fr;
    }

    .sales-grid {
      grid-template-columns: 1fr;
    }

    .status-grid {
      grid-template-columns: 1fr;
    }

    .user-detail-container {
      padding: 0;
      max-height: 80vh;
    }

    .avatar-container {
      margin-bottom: 16px;
      text-align: center;
    }

    .info-item {
      margin-bottom: 8px;
      display: flex;
      flex-direction: column;
      align-items: flex-start;
    }

    .info-item label {
      font-size: 12px;
      margin-bottom: 4px;
    }

    .status-item {
      flex-direction: column;
      align-items: flex-start;
      gap: 4px;
    }

    .status-item label {
      min-width: auto;
      font-size: 12px;
    }

    .data-item {
      padding: 12px;
    }

    .sales-item {
      padding: 16px;
    }

    .sales-value,
    .data-value {
      font-size: 16px;
    }

    .remark-content {
      padding: 8px;
      font-size: 14px;
    }

    .detail-card {
      margin-bottom: 12px;
    }

    .card-header h3 {
      font-size: 14px;
    }
  }

  /* 操作按钮样式优化 */
  .operation-buttons {
    display: flex;
    flex-direction: column;
    gap: 6px;
    align-items: center;
    padding: 8px 4px;
    min-height: 100px;
    justify-content: center;
  }

  .button-row {
    display: flex;
    gap: 6px;
    justify-content: center;
    align-items: center;
    width: 100%;
  }

  .button-row .el-button {
    min-width: 80px;
    height: 28px;
    padding: 4px 8px;
    font-size: 12px;
    border-radius: 4px;
    flex: 0 0 auto;
  }

  /* 确保所有按钮大小一致 */
  .operation-buttons .el-button {
    box-sizing: border-box;
  }

  /* 更多按钮样式调整 */
  .button-row:last-child {
    margin-top: 2px;
  }

  /* 移动端操作按钮适配 */
  @media (max-width: 768px) {
    .operation-buttons {
      gap: 4px;
      padding: 4px 2px;
      min-height: 80px;
    }

    .button-row .el-button {
      min-width: 70px;
      height: 24px;
      padding: 2px 6px;
      font-size: 11px;
    }

    .button-row {
      gap: 4px;
    }
  }
</style>
