<template>
  <div v-loading.fullscreen.lock="fullscreenLoading">
    <div class="flex flex-col md:flex-row gap-6 p-4">
      <!-- 左侧分类树 -->
      <CategoryTree
        :categories="categories"
        :selected-category-id="search.categoryId"
        @node-click="handleNodeClick"
        @add-category="addCategoryFun"
        @edit-category="editCategory"
        @delete-category="deleteCategoryFun"
      />

      <!-- 右侧内容区 -->
      <div class="flex-1 bg-white text-slate-700 dark:text-slate-400 dark:bg-slate-900 rounded-lg shadow-sm">
        <!-- 搜索和操作区域 -->
        <SearchBar
          v-model:search="search"
          @search="getTableData"
          @refresh-all="checkAllUsersStatus"
          @filter-invalid="filterInvalidLogin"
          @show-auth-dialog="showSearchUniqueIdDialog"
        />

        <!-- 账号表格 -->
        <AccountTable
          :table-data="tableData"
          :page="page"
          :page-size="pageSize"
          :total="total"
          :categories="categories"
          :video-category-map="videoCategoryMap"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
          @update-category="showUpdateCategoryDialog"
          @check-login="checkLoginStatus"
          @show-bind-info="showBindInfoDialog"
          @handle-real-name="showRealNameDialog"
          @handle-phone="showPhoneDialog"
          @show-remark="showRemarkDialog"
          @move-up="moveUp"
          @move-down="moveDown"
          @show-publish-dialog="showAutoPublishDialog"
          @show-marketing-dialog="showContentSettingDialog"
          @single-flush="singleFlush"
          @view-awemes="viewAwemes"
          @show-user-detail="showUserDetailDialog"
          @disable-product="disableProduct"
          @handle-product="handleProduct"
          @delete-user="deleteUserFun"
        />
      </div>
    </div>

    <!-- 分类管理弹窗 -->
    <CategoryDialog
      v-model:visible="categoryDialogVisible"
      v-model:updateVisible="updateCategoryDialogVisible"
      :categories="categories"
      :form-data="categoryFormData"
      :update-form-data="updateCategoryFormData"
      @confirm="confirmAddCategory"
      @update-confirm="confirmUpdateCategory"
      @close="closeAddCategoryDialog"
    />

    <!-- 账号授权弹窗 -->
    <AuthDialog
      v-model:searchVisible="searchUniqueIdDialogVisible"
      v-model:qrCodeVisible="getQrCodeDialogVisible"
      :categories="categories"
      :search-form="searchUniqueIdForm"
      :auth-form="authForm"
      :show-proxy-form="searchProxyFormItem"
      :fit-proxy-channel="fitProxyChannel"
      :fit-proxy-keyword="fitProxyKeyword"
      @search-proxy-fit="searchProxyFit"
      @get-independent-ip="getIndependentIP"
      @fit-proxy-search="fitProxySearch"
      @handle-fit-proxy-select="handleFitProxySelect"
      @auth-get-qr-code="authGetQrCode"
      @start-polling="startPolling"
      @stop-polling="stopPolling"
      @valid-sms="validSms"
    />

    <!-- 发布设定弹窗 -->
    <PublishDialog
      v-model:visible="autoPublishDialogVisible"
      v-model:templateVisible="autoPublishTemplateDialogVisible"
      :template-name-list="publishTemplateNameList"
      :current-day="publishCurrentDay"
      :current-type="publishCurrentType"
      :current-video-category="publishCurrentVideoCategory"
      :publish-data="currentPublishData"
      :video-category-list="videoCategoryList"
      :current-user="currentPublishUser"
      :template-name="publishTemplateCurrentName"
      :template-current-day="publishTemplateCurrentDay"
      :template-data="currentPublishTemplateData"
      @close="closeAutoPublishDialog"
      @choose-template="chooseTemplate"
      @edit-template="editTemplate"
      @delete-template="deleteTemplate"
      @show-template-dialog="showAutoPublishTemplateDialog"
      @clear-publish-data="clearPublishData"
      @reset-publish-data="resetPublishData"
      @change-status="changeAutoPublishStatus"
      @confirm="confirmAutoPublish"
      @close-template="closeAutoPublishTemplateDialog"
      @clear-template-data="clearTemplateData"
      @confirm-template="confirmAutoPublishTemplate"
    />

    <!-- 营销设定弹窗 -->
    <MarketingDialog
      v-model:visible="contentSettingDialogVisible"
      :form-data="contentSettingForm"
      :open-browser-loading="openBrowserLoading"
      :is-open-browser="isOpenBrowser"
      @close="closeContentSettingDialog"
      @save="saveContentSetting"
      @get-promotion-link="getPromotionLink"
      @handle-talk-auth="handleTalkAuth"
      @clear-talk-auth="clearTalkAuth"
      @confirm-login-bite="confirmLoginBite"
      @update-track-status="updateTrackStatus"
      @update-form-field="updateMarketingFormField"
    />

    <!-- 用户详情弹窗 -->
    <UserDetailDialog
      v-model:visible="userDetailDialogVisible"
      :user-detail="currentUserDetail"
      :categories="categories"
      @check-ip-health="checkIPHealth"
    />

    <!-- 其他弹窗组件 -->
    <MiscDialogs
      v-model:updateIPVisible="updateIPDialogVisible"
      v-model:realNameVisible="realNameDialogVisible"
      v-model:phoneVisible="phoneDialogVisible"
      v-model:remarkVisible="remarkDialogVisible"
      v-model:bindInfoVisible="bindInfoDialogVisible"
      v-model:awemeVisible="awemeDialogVisible"
      :update-i-p-form="updateIPForm"
      :real-name-form="realNameForm"
      :phone-form="phoneForm"
      :remark-form="remarkForm"
      :bind-info="currentBindInfo"
      :same-i-p-users="sameIPUsers"
      :same-i-p-loading="sameIPUsersLoading"
      :selected-user="selectedUser"
      @clear-ip="clearIP"
      @confirm-update-ip="confirmUpdateIP"
      @clear-real-name="clearRealName"
      @confirm-real-name="confirmRealName"
      @phone-search="phoneSearch"
      @phone-selected="phoneSelected"
      @clear-phone="clearPhone"
      @confirm-phone="confirmPhone"
      @confirm-remark="confirmRemark"
      @show-update-ip="showUpdateIPDialog"
      @check-ip-health="checkIPHealth"
      @refresh-same-ip="refreshSameIPUsers"
      @update-form-field="updateFormField"
    />
  </div>
</template>

<script setup>
  import { ref, nextTick, onMounted, onUnmounted } from 'vue'
  import { ElMessage, ElMessageBox } from 'element-plus'

  // 导入子组件
  import CategoryTree from '@/components/dyUser/CategoryTree.vue'
  import SearchBar from '@/components/dyUser/SearchBar.vue'
  import AccountTable from '@/components/dyUser/AccountTable.vue'
  import CategoryDialog from '@/components/dyUser/CategoryDialog.vue'
  import AuthDialog from '@/components/dyUser/AuthDialog.vue'
  import PublishDialog from '@/components/dyUser/PublishDialog.vue'
  import MarketingDialog from '@/components/dyUser/MarketingDialog.vue'
  import UserDetailDialog from '@/components/dyUser/UserDetailDialog.vue'
  import MiscDialogs from '@/components/dyUser/MiscDialogs.vue'

  // 导入API
  import {
    getUserList,
    deleteUser,
    toggleProductEnabled as toggleProductEnabledAPI,
    updateUserIP,
    bindDevice,
    updateCategory,
    updateUserRemark,
    updateCommentSetting,
    updateUserTalkAuthStatus,
    clearUserTalkAuthStatus,
    updateUserBiteBrowserId,
    updateUserPromotionLink,
    updateUserTraceStatus,
    fuzzyMatchUsersByProxyPort,
    fuzzyMatchUsersByUniqueId,
    fuzzyMatchUsersByMac,
    saveUserSort
  } from '@/api/douyin/dyUser'
  import { getProductFeed } from '@/api/douyin/dyProduct'
  import { getAvailableIP, getUnusedIP, checkSingleIP, getIPBindings } from '@/api/douyin/ip'
  import {
    getQrCode,
    checkQrCode,
    validCaptcha,
    getUserInfoForMore,
    getUserLoginInfoForMore,
    checkAllUserStatusForMore
  } from '@/api/douyin/douyinForMore'
  import { addCategory, deleteCategory, getCategoryList } from '@/api/dyUserCategory'
  import { findPhone } from '@/api/douyin/phoneBalance'
  import { getVideoCategoryList } from '@/api/creative/videoCategory'
  import {
    getAutoPublishVideoListByDyUserId,
    saveAutoPublishVideo,
    changeAutoPublishVideoStauts
  } from '@/api/creative/autoPublishVideo'
  import { createBrowser, openBrowser, deleteBrowser, getBrowserInfo, getCookie } from '@/api/biteBrowser'
  import { saveDouyinWebCookie } from '@/api/douyin/chat'
  import {
    getPublishVideoTemplateNameList,
    getPublishVideoTemplateList,
    savePublishVideoTemplate,
    deletePublishVideoTemplate
  } from '@/api/creative/autoPublishVideoTemplate'
  import Sortable from 'sortablejs'

  defineOptions({
    name: 'AccountList'
  })

  // 基础数据
  const page = ref(1)
  const total = ref(0)
  const pageSize = ref(100)
  const search = ref({
    nickname: null,
    uniqueId: null,
    categoryId: 0,
    invalidLogin: null
  })
  const tableData = ref([])

  const sortableInstance = ref(null)
  const fullscreenLoading = ref(false)

  // 分类相关
  const categories = ref([])
  const categoryDialogVisible = ref(false)
  const updateCategoryDialogVisible = ref(false)
  const categoryFormData = ref({
    ID: 0,
    pid: 0,
    name: ''
  })
  const updateCategoryFormData = ref({
    ID: null,
    categoryId: null
  })

  // 授权相关
  const searchUniqueIdDialogVisible = ref(false)
  const getQrCodeDialogVisible = ref(false)
  const searchUniqueIdForm = ref({
    uniqueId: null,
    accountType: null,
    categoryId: null,
    proxy: null
  })
  const authForm = ref({
    categoryId: null,
    did: '',
    accountType: null,
    token: null,
    showCaptchaInput: null,
    iid: null,
    proxy: null,
    qr_code_url: null,
    code: null,
    warningText: null
  })
  const searchProxyFormItem = ref(false)
  const fitProxyChannel = ref(null)
  const fitProxyKeyword = ref(null)
  const checkingQrCodeFlag = ref(false)

  // 发布设定相关
  const autoPublishDialogVisible = ref(false)
  const autoPublishTemplateDialogVisible = ref(false)
  const currentPublishUser = ref(null)
  const publishCurrentDay = ref(1)
  const publishCurrentType = ref(1)
  const publishCurrentVideoCategory = ref(null)
  const currentPublishData = ref({
    1: [],
    2: [],
    3: [],
    4: [],
    5: [],
    6: [],
    7: []
  })
  const videoCategoryList = ref([])
  const videoCategoryMap = ref({})
  const publishTemplateNameList = ref([])
  const publishTemplateCurrentDay = ref(1)
  const publishTemplateCurrentId = ref(null)
  const publishTemplateCurrentName = ref(null)
  const currentPublishTemplateData = ref({
    1: [],
    2: [],
    3: [],
    4: [],
    5: [],
    6: [],
    7: []
  })

  // 营销设定相关
  const contentSettingDialogVisible = ref(false)
  const contentSettingForm = ref({
    ID: null,
    commentTemplates: '',
    commentTraceStatus: 0,
    commentKeyword: '',
    talkAuthStatus: 0,
    promotionLink: '',
    biteBrowserId: '',
    proxy: '',
    uniqueId: '',
    replyTemplates: ''
  })
  const isOpenBrowser = ref(false)
  const openBrowserLoading = ref(false)
  const biteBrowserInfo = ref({
    proxyMethod: 2,
    proxyType: 'http',
    proxyUserName: 'smarwpto',
    proxyPassword: '9a07e0r3',
    url: 'https://www.douyin.com'
  })

  // 用户详情相关
  const userDetailDialogVisible = ref(false)
  const currentUserDetail = ref({})

  // 其他弹窗相关
  const updateIPDialogVisible = ref(false)
  const realNameDialogVisible = ref(false)
  const phoneDialogVisible = ref(false)
  const remarkDialogVisible = ref(false)
  const bindInfoDialogVisible = ref(false)
  const awemeDialogVisible = ref(false)
  const updateIPForm = ref({
    id: null,
    nickname: '',
    currentIP: '',
    newIP: '',
    availableIPs: []
  })
  const realNameForm = ref({
    id: null,
    nickname: '',
    realName: ''
  })
  const phoneForm = ref({
    id: null,
    nickname: '',
    phone: '',
    selectedPhone: null
  })
  const remarkForm = ref({
    id: null,
    nickname: '',
    remark: ''
  })
  const currentBindInfo = ref({
    bindIP: '',
    did: '',
    iid: '',
    ID: null,
    nickname: '',
    ipSort: null,
    ipId: null
  })
  const sameIPUsers = ref({
    dyUsers: [],
    flameUsers: []
  })
  const sameIPUsersLoading = ref(false)
  const selectedUser = ref({})

  // 分页处理
  const handleSizeChange = (val) => {
    pageSize.value = val
    getTableData()
  }

  const handleCurrentChange = (val) => {
    page.value = val
    getTableData()
  }

  // 查询数据
  const getTableData = async () => {
    await fetchVideoCategoryList()

    const table = await getUserList({
      page: page.value,
      pageSize: pageSize.value,
      ...search.value
    })
    if (table.code === 0) {
      tableData.value = table.data.list
      total.value = table.data.total
      page.value = table.data.page
      pageSize.value = table.data.pageSize
    }

    await nextTick()
    initSortable()
  }

  // 初始化拖拽排序
  const initSortable = () => {
    if (sortableInstance.value) {
      sortableInstance.value.destroy()
    }

    const tbody = document.querySelector('.el-table__body-wrapper tbody')
    if (!tbody) return

    sortableInstance.value = Sortable.create(tbody, {
      handle: '.drag-handle',
      animation: 150,
      onEnd: async (evt) => {
        const { oldIndex, newIndex } = evt
        if (oldIndex === newIndex) return

        const movedItem = tableData.value.splice(oldIndex, 1)[0]
        tableData.value.splice(newIndex, 0, movedItem)

        await saveSort()
      }
    })
  }

  // 保存排序
  const saveSort = async () => {
    try {
      const sortData = tableData.value.map((item, index) => ({
        id: item.ID,
        sort: index + 1
      }))

      const params = search.value.categoryId ? { categoryId: search.value.categoryId } : {}
      await saveUserSort({ ...params, sortData })
      ElMessage.success('排序保存成功')
    } catch (error) {
      console.error('保存排序失败:', error)
      ElMessage.error('保存排序失败')
    }
  }

  // 移动行
  const moveUp = (index) => {
    if (index === 0) return
    const temp = tableData.value[index]
    tableData.value[index] = tableData.value[index - 1]
    tableData.value[index - 1] = temp
    saveSort()
  }

  const moveDown = (index) => {
    if (index === tableData.value.length - 1) return
    const temp = tableData.value[index]
    tableData.value[index] = tableData.value[index + 1]
    tableData.value[index + 1] = temp
    saveSort()
  }

  // 获取分类列表
  const fetchCategories = async () => {
    const res = await getCategoryList()
    if (res.code === 0) {
      categories.value = res.data
    }
  }

  // 获取视频分类列表
  const fetchVideoCategoryList = async () => {
    const res = await getVideoCategoryList()
    if (res.code === 0) {
      videoCategoryList.value = res.data
      videoCategoryMap.value = res.data.reduce((map, item) => {
        map[item.ID] = item.name
        return map
      }, {})
    }
  }

  // 分类树节点点击
  const handleNodeClick = (node) => {
    search.value.categoryId = node.ID
    getTableData()
  }

  // 分类管理方法
  const addCategoryFun = (data) => {
    categoryFormData.value = {
      ID: 0,
      pid: data.ID,
      name: ''
    }
    categoryDialogVisible.value = true
  }

  const editCategory = (data) => {
    categoryFormData.value = {
      ID: data.ID,
      pid: data.pid,
      name: data.name
    }
    categoryDialogVisible.value = true
  }

  const deleteCategoryFun = async (id) => {
    const confirm = await ElMessageBox.confirm('确定要删除这个分类吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    if (confirm !== 'confirm') return

    const res = await deleteCategory({ ID: id })
    if (res.code === 0) {
      ElMessage.success('删除成功')
      fetchCategories()
    } else {
      ElMessage.error('删除失败：' + res.msg)
    }
  }

  const confirmAddCategory = async (formData) => {
    const res = await addCategory(formData)
    if (res.code === 0) {
      ElMessage.success('操作成功')
      categoryDialogVisible.value = false
      fetchCategories()
    } else {
      ElMessage.error('操作失败：' + res.msg)
    }
  }

  const closeAddCategoryDialog = () => {
    categoryDialogVisible.value = false
    categoryFormData.value = {
      ID: 0,
      pid: 0,
      name: ''
    }
  }

  // 更新分类弹窗
  const showUpdateCategoryDialog = (row) => {
    updateCategoryFormData.value = {
      ID: row.ID,
      categoryId: row.categoryId
    }
    updateCategoryDialogVisible.value = true
  }

  const confirmUpdateCategory = async (formData) => {
    const res = await updateCategory(formData)
    if (res.code === 0) {
      ElMessage.success('更新成功')
      updateCategoryDialogVisible.value = false
      getTableData()
    } else {
      ElMessage.error('更新失败：' + res.msg)
    }
  }

  // 授权相关方法
  const showSearchUniqueIdDialog = () => {
    searchUniqueIdForm.value = {
      uniqueId: null,
      accountType: 1,
      categoryId: search.value.categoryId,
      proxy: null
    }
    searchUniqueIdDialogVisible.value = true
  }

  // 授权相关方法
  const searchProxyFit = () => {
    searchProxyFormItem.value = true
  }

  const getIndependentIP = async () => {
    try {
      const res = await getUnusedIP()
      if (res.code === 0 && res.data) {
        searchUniqueIdForm.value.proxy = res.data
        ElMessage.success('获取独立IP成功')
      } else {
        ElMessage.error('获取独立IP失败：' + res.msg)
      }
    } catch (error) {
      ElMessage.error('获取独立IP失败')
    }
  }

  const fitProxySearch = async (queryString, cb) => {
    if (!queryString || !fitProxyChannel.value) {
      cb([])
      return
    }

    try {
      let res
      if (fitProxyChannel.value === '1') {
        res = await fuzzyMatchUsersByUniqueId({ uniqueId: queryString })
      } else if (fitProxyChannel.value === '2') {
        res = await fuzzyMatchUsersByProxyPort({ proxyPort: queryString })
      } else if (fitProxyChannel.value === '3') {
        res = await fuzzyMatchUsersByMac({ mac: queryString })
      }

      if (res.code === 0) {
        cb(res.data || [])
      } else {
        cb([])
      }
    } catch (error) {
      cb([])
    }
  }

  const handleFitProxySelect = (item) => {
    if (item.bindIP) {
      searchUniqueIdForm.value.proxy = item.bindIP
      ElMessage.success('已选择匹配的代理IP')
    }
  }

  const authGetQrCode = async () => {
    if (!searchUniqueIdForm.value.categoryId) {
      ElMessage.error('请先选择分类')
      return
    }

    authForm.value.uniqueId = searchUniqueIdForm.value.uniqueId
    authForm.value.categoryId = searchUniqueIdForm.value.categoryId
    authForm.value.accountType = searchUniqueIdForm.value.accountType
    authForm.value.proxy = searchUniqueIdForm.value.proxy

    searchUniqueIdDialogVisible.value = false
    await showGetQrCodeDialog()
  }

  const showGetQrCodeDialog = async () => {
    try {
      const res = await getQrCode({
        uniqueId: authForm.value.uniqueId,
        categoryId: authForm.value.categoryId,
        accountType: authForm.value.accountType,
        proxy: authForm.value.proxy
      })

      if (res.code === 0) {
        authForm.value.did = res.data.did
        authForm.value.iid = res.data.iid
        authForm.value.token = res.data.token
        authForm.value.qr_code_url = res.data.qr_code_url
        authForm.value.showCaptchaInput = false
        authForm.value.warningText = null
        getQrCodeDialogVisible.value = true
      } else {
        ElMessage.error('获取二维码失败：' + res.msg)
      }
    } catch (error) {
      ElMessage.error('获取二维码失败')
    }
  }

  const startPolling = () => {
    if (checkingQrCodeFlag.value) return
    checkingQrCodeFlag.value = true
    checkQrCodeStatus()
  }

  const stopPolling = () => {
    checkingQrCodeFlag.value = false
  }

  const checkQrCodeStatus = async () => {
    if (!checkingQrCodeFlag.value) return

    try {
      const res = await checkQrCode({
        token: authForm.value.token,
        did: authForm.value.did,
        iid: authForm.value.iid
      })

      if (res.code === 0) {
        if (res.data.status === 'success') {
          ElMessage.success('登录成功')
          getQrCodeDialogVisible.value = false
          checkingQrCodeFlag.value = false
          getTableData()
        } else if (res.data.status === 'need_captcha') {
          authForm.value.showCaptchaInput = true
          authForm.value.warningText = '需要验证码验证'
          checkingQrCodeFlag.value = false
        } else if (res.data.status === 'waiting') {
          setTimeout(() => {
            if (checkingQrCodeFlag.value) {
              checkQrCodeStatus()
            }
          }, 2000)
        } else {
          authForm.value.warningText = res.data.message || '登录失败'
          checkingQrCodeFlag.value = false
        }
      } else {
        authForm.value.warningText = res.msg
        checkingQrCodeFlag.value = false
      }
    } catch (error) {
      authForm.value.warningText = '检查登录状态失败'
      checkingQrCodeFlag.value = false
    }
  }

  const validSms = async () => {
    try {
      const res = await validCaptcha({
        token: authForm.value.token,
        code: authForm.value.code,
        did: authForm.value.did,
        iid: authForm.value.iid
      })

      if (res.code === 0) {
        ElMessage.success('验证成功，登录完成')
        getQrCodeDialogVisible.value = false
        getTableData()
      } else {
        ElMessage.error('验证失败：' + res.msg)
      }
    } catch (error) {
      ElMessage.error('验证失败')
    }
  }

  // 检查登录状态
  const checkLoginStatus = async (row) => {
    try {
      const res = await getUserLoginInfoForMore(row, 1, 0)
      if (res.msg !== '成功') {
        ElMessage.warning('用户登录失效，正在重新获取登录二维码...')
        authForm.value.uniqueId = row.uniqueId
        authForm.value.categoryId = row.categoryId
        authForm.value.accountType = row.accountType
        if (res.data && res.data.ID) {
          row.status = res.data.status
        } else {
          row.status = 0
        }
        await showGetQrCodeDialog()
      } else {
        ElMessage.success('刷新成功')
        row.awemeCount = res.data.awemeCount
        row.followerCount = res.data.followerCount
        row.likeCount = res.data.likeCount
        row.status = res.data.status
      }
    } catch (err) {
      console.error('检查登录状态失败:', err.message)
      ElMessage.error('检查登录状态失败：' + err.message)
    }
  }

  // 刷新所有用户状态
  const checkAllUsersStatus = async () => {
    try {
      ElMessage.warning('刷新全部可能需要较长时间，请稍后...')
      const res = await checkAllUserStatusForMore({ categoryId: search.value.categoryId })
      if (res.code !== 0) {
        ElMessage.error('刷新失败：' + res.msg)
      }
      ElMessage.success('刷新完成')
      getTableData()
    } catch (error) {
      console.error('刷新失败:', error)
      ElMessage.error('刷新失败')
    }
  }

  // 筛选未登录用户
  const filterInvalidLogin = () => {
    search.value.invalidLogin = search.value.invalidLogin === 1 ? null : 1
    getTableData()
  }

  // 单个刷新
  const singleFlush = async (row) => {
    try {
      const res = await getUserInfoForMore(row)
      if (res.code === 0) {
        ElMessage.success('刷新成功')
        Object.assign(row, res.data)
      } else {
        ElMessage.error('刷新失败：' + res.msg)
      }
    } catch (error) {
      ElMessage.error('刷新失败')
    }
  }

  // 发布设定相关方法
  const showAutoPublishDialog = async (row) => {
    currentPublishUser.value = row
    publishCurrentVideoCategory.value = row.videoCategoryId

    // 获取发布模板列表
    try {
      const res = await getPublishVideoTemplateNameList()
      if (res.code === 0) {
        publishTemplateNameList.value = res.data
      }
    } catch (error) {
      console.error('获取发布模板失败:', error)
    }

    // 获取用户的发布设置
    try {
      const res = await getAutoPublishVideoListByDyUserId({ dyUserId: row.ID })
      if (res.code === 0 && res.data.length > 0) {
        const publishData = res.data[0]
        publishCurrentDay.value = 1
        publishCurrentType.value = publishData.type || 1

        // 解析发布时间数据
        const timeData = JSON.parse(publishData.publishTime || '{}')
        currentPublishData.value = {
          1: timeData[1] || [],
          2: timeData[2] || [],
          3: timeData[3] || [],
          4: timeData[4] || [],
          5: timeData[5] || [],
          6: timeData[6] || [],
          7: timeData[7] || []
        }
      } else {
        // 重置为默认值
        resetPublishData()
      }
    } catch (error) {
      console.error('获取发布设置失败:', error)
      resetPublishData()
    }

    autoPublishDialogVisible.value = true
  }

  const resetPublishData = () => {
    currentPublishData.value = {
      1: [8, 12, 18, 21],
      2: [8, 12, 18, 21],
      3: [8, 12, 18, 21],
      4: [8, 12, 18, 21],
      5: [8, 12, 18, 21],
      6: [8, 12, 18, 21],
      7: [8, 12, 18, 21]
    }
  }

  const clearPublishData = () => {
    currentPublishData.value = {
      1: [],
      2: [],
      3: [],
      4: [],
      5: [],
      6: [],
      7: []
    }
  }

  const closeAutoPublishDialog = () => {
    autoPublishDialogVisible.value = false
    currentPublishUser.value = null
  }

  const confirmAutoPublish = async () => {
    if (!publishCurrentVideoCategory.value) {
      ElMessage.error('请选择视频分类')
      return
    }

    try {
      const res = await saveAutoPublishVideo({
        dyUserId: currentPublishUser.value.ID,
        videoCategoryId: publishCurrentVideoCategory.value,
        type: publishCurrentType.value,
        publishTime: JSON.stringify(currentPublishData.value)
      })

      if (res.code === 0) {
        ElMessage.success('保存成功')
        autoPublishDialogVisible.value = false
        getTableData()
      } else {
        ElMessage.error('保存失败：' + res.msg)
      }
    } catch (error) {
      ElMessage.error('保存失败')
    }
  }

  const changeAutoPublishStatus = async (status) => {
    try {
      const res = await changeAutoPublishVideoStauts({
        dyUserId: currentPublishUser.value.ID,
        status: status
      })

      if (res.code === 0) {
        ElMessage.success('状态更新成功')
        getTableData()
      } else {
        ElMessage.error('状态更新失败：' + res.msg)
      }
    } catch (error) {
      ElMessage.error('状态更新失败')
    }
  }

  // 发布模板相关方法
  const showAutoPublishTemplateDialog = () => {
    publishTemplateCurrentName.value = ''
    publishTemplateCurrentDay.value = 1
    clearTemplateData()
    autoPublishTemplateDialogVisible.value = true
  }

  const clearTemplateData = () => {
    currentPublishTemplateData.value = {
      1: [],
      2: [],
      3: [],
      4: [],
      5: [],
      6: [],
      7: []
    }
  }

  const closeAutoPublishTemplateDialog = () => {
    autoPublishTemplateDialogVisible.value = false
  }

  const confirmAutoPublishTemplate = async () => {
    if (!publishTemplateCurrentName.value) {
      ElMessage.error('请输入模板名称')
      return
    }

    try {
      const res = await savePublishVideoTemplate({
        templateId: publishTemplateCurrentId.value,
        templateName: publishTemplateCurrentName.value,
        publishTime: JSON.stringify(currentPublishTemplateData.value)
      })

      if (res.code === 0) {
        ElMessage.success('模板保存成功')
        autoPublishTemplateDialogVisible.value = false

        // 刷新模板列表
        const templateRes = await getPublishVideoTemplateNameList()
        if (templateRes.code === 0) {
          publishTemplateNameList.value = templateRes.data
        }
      } else {
        ElMessage.error('模板保存失败：' + res.msg)
      }
    } catch (error) {
      ElMessage.error('模板保存失败')
    }
  }

  const chooseTemplate = async (template) => {
    try {
      const res = await getPublishVideoTemplateList({ templateId: template.templateId })
      if (res.code === 0 && res.data.length > 0) {
        const templateData = JSON.parse(res.data[0].publishTime || '{}')
        currentPublishData.value = {
          1: templateData[1] || [],
          2: templateData[2] || [],
          3: templateData[3] || [],
          4: templateData[4] || [],
          5: templateData[5] || [],
          6: templateData[6] || [],
          7: templateData[7] || []
        }
        ElMessage.success('模板应用成功')
      }
    } catch (error) {
      ElMessage.error('应用模板失败')
    }
  }

  const editTemplate = async (template) => {
    try {
      const res = await getPublishVideoTemplateList({ templateId: template.templateId })
      if (res.code === 0 && res.data.length > 0) {
        publishTemplateCurrentId.value = template.templateId
        publishTemplateCurrentName.value = template.templateName

        const templateData = JSON.parse(res.data[0].publishTime || '{}')
        currentPublishTemplateData.value = {
          1: templateData[1] || [],
          2: templateData[2] || [],
          3: templateData[3] || [],
          4: templateData[4] || [],
          5: templateData[5] || [],
          6: templateData[6] || [],
          7: templateData[7] || []
        }

        autoPublishTemplateDialogVisible.value = true
      }
    } catch (error) {
      ElMessage.error('获取模板失败')
    }
  }

  const deleteTemplate = async (template) => {
    const confirm = await ElMessageBox.confirm('确定要删除这个模板吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    if (confirm !== 'confirm') return

    try {
      const res = await deletePublishVideoTemplate({ templateId: template.templateId })
      if (res.code === 0) {
        ElMessage.success('删除成功')

        // 刷新模板列表
        const templateRes = await getPublishVideoTemplateNameList()
        if (templateRes.code === 0) {
          publishTemplateNameList.value = templateRes.data
        }
      } else {
        ElMessage.error('删除失败：' + res.msg)
      }
    } catch (error) {
      ElMessage.error('删除失败')
    }
  }

  // 营销设定相关方法
  const showContentSettingDialog = (row) => {
    contentSettingForm.value = {
      ID: row.ID,
      commentTemplates: row.commentTemplates || '',
      commentTraceStatus: row.commentTraceStatus || 0,
      commentKeyword: row.commentKeyword || '',
      talkAuthStatus: row.talkAuthStatus || 0,
      promotionLink: row.promotionLink || '',
      biteBrowserId: row.biteBrowserId || '',
      proxy: row.bindIP || '',
      uniqueId: row.uniqueId || '',
      replyTemplates: row.replyTemplates || ''
    }
    contentSettingDialogVisible.value = true
  }

  const closeContentSettingDialog = () => {
    contentSettingDialogVisible.value = false
  }

  const saveContentSetting = async () => {
    try {
      const res = await updateCommentSetting(contentSettingForm.value)
      if (res.code === 0) {
        ElMessage.success('保存成功')
        contentSettingDialogVisible.value = false
        getTableData()
      } else {
        ElMessage.error('保存失败：' + res.msg)
      }
    } catch (error) {
      ElMessage.error('保存失败')
    }
  }

  const getPromotionLink = async (formData) => {
    try {
      const res = await getProductFeed({ dyUserId: formData.ID })
      if (res.code === 0 && res.data) {
        formData.promotionLink = res.data

        // 更新数据库
        await updateUserPromotionLink({
          id: formData.ID,
          promotionLink: res.data
        })

        ElMessage.success('获取推广链接成功')
      } else {
        ElMessage.error('获取推广链接失败：' + res.msg)
      }
    } catch (error) {
      ElMessage.error('获取推广链接失败')
    }
  }

  const handleTalkAuth = async (formData, status) => {
    if (status === 1) {
      // 启用授权，需要打开比特浏览器
      openBrowserLoading.value = true
      isOpenBrowser.value = true

      try {
        // 创建或更新浏览器配置
        const browserRes = await createBrowser({
          name: `DY_${formData.uniqueId}`,
          proxyMethod: biteBrowserInfo.value.proxyMethod,
          proxyType: biteBrowserInfo.value.proxyType,
          proxyHost: formData.proxy.split(':')[0],
          proxyPort: formData.proxy.split(':')[1],
          proxyUserName: biteBrowserInfo.value.proxyUserName,
          proxyPassword: biteBrowserInfo.value.proxyPassword,
          url: biteBrowserInfo.value.url
        })

        if (browserRes.code === 0) {
          formData.biteBrowserId = browserRes.data.id

          // 打开浏览器
          await openBrowser({ id: browserRes.data.id })

          // 更新数据库中的浏览器ID
          await updateUserBiteBrowserId({
            id: formData.ID,
            biteBrowserId: browserRes.data.id
          })

          ElMessage.success('浏览器已打开，请在浏览器中登录抖音')
        } else {
          ElMessage.error('创建浏览器失败：' + browserRes.msg)
        }
      } catch (error) {
        ElMessage.error('打开浏览器失败')
      } finally {
        openBrowserLoading.value = false
      }
    } else if (status === 3) {
      // 禁用授权
      try {
        const res = await updateUserTalkAuthStatus({
          id: formData.ID,
          talkAuthStatus: 3
        })

        if (res.code === 0) {
          formData.talkAuthStatus = 3
          ElMessage.success('已禁用营销授权')
          getTableData()
        } else {
          ElMessage.error('禁用失败：' + res.msg)
        }
      } catch (error) {
        ElMessage.error('禁用失败')
      }
    }
  }

  const clearTalkAuth = async (formData) => {
    const confirm = await ElMessageBox.confirm('确认清空营销授权吗？该操作不可恢复', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    if (confirm !== 'confirm') return

    try {
      // 获取浏览器窗口详情
      const infoRes = await getBrowserInfo({ id: formData.biteBrowserId })
      if (!infoRes) {
        ElMessage.error('获取浏览器窗口失败')
        return
      }

      // 清空数据库状态
      const res = await clearUserTalkAuthStatus({ id: formData.ID })
      if (res.code !== 0) {
        ElMessage.error('更新授权状态失败：' + res.msg)
        return
      }

      // 删除浏览器
      await deleteBrowser({ id: formData.biteBrowserId })

      formData.talkAuthStatus = 0
      formData.biteBrowserId = ''
      ElMessage.success('清空授权成功')
      getTableData()
    } catch (error) {
      ElMessage.error('清空授权失败')
    }
  }

  const confirmLoginBite = async () => {
    try {
      // 获取cookie并保存
      const cookieRes = await getCookie({ id: contentSettingForm.value.biteBrowserId })
      if (cookieRes.code === 0) {
        const saveRes = await saveDouyinWebCookie({
          dyUserId: contentSettingForm.value.ID,
          cookie: cookieRes.data
        })

        if (saveRes.code === 0) {
          // 更新授权状态
          await updateUserTalkAuthStatus({
            id: contentSettingForm.value.ID,
            talkAuthStatus: 1
          })

          contentSettingForm.value.talkAuthStatus = 1
          isOpenBrowser.value = false
          ElMessage.success('授权成功')
          getTableData()
        } else {
          ElMessage.error('保存Cookie失败：' + saveRes.msg)
        }
      } else {
        ElMessage.error('获取Cookie失败：' + cookieRes.msg)
      }
    } catch (error) {
      ElMessage.error('确认登录失败')
    }
  }

  const updateTrackStatus = async (status) => {
    try {
      const res = await updateUserTraceStatus({
        id: contentSettingForm.value.ID,
        commentTraceStatus: status
      })

      if (res.code === 0) {
        contentSettingForm.value.commentTraceStatus = status
        ElMessage.success('状态更新成功')
        getTableData()
      } else {
        ElMessage.error('状态更新失败：' + res.msg)
      }
    } catch (error) {
      ElMessage.error('状态更新失败')
    }
  }

  // 用户详情相关方法
  const showUserDetailDialog = (row) => {
    currentUserDetail.value = { ...row }
    userDetailDialogVisible.value = true
  }

  // 产品相关方法
  const handleProduct = async (row) => {
    try {
      const res = await toggleProductEnabledAPI({
        id: row.ID,
        isProductEnabled: true
      })

      if (res.code === 0) {
        row.isProductEnabled = true
        ElMessage.success('开启选品成功')
      } else {
        ElMessage.error('开启选品失败：' + res.msg)
      }
    } catch (error) {
      ElMessage.error('开启选品失败')
    }
  }

  const disableProduct = async (row) => {
    const confirm = await ElMessageBox.confirm('确定要关闭选品吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    if (confirm !== 'confirm') return

    try {
      const res = await toggleProductEnabledAPI({
        id: row.ID,
        isProductEnabled: false
      })

      if (res.code === 0) {
        row.isProductEnabled = false
        ElMessage.success('关闭选品成功')
      } else {
        ElMessage.error('关闭选品失败：' + res.msg)
      }
    } catch (error) {
      ElMessage.error('关闭选品失败')
    }
  }

  // 删除用户
  const deleteUserFun = async (row) => {
    const confirm = await ElMessageBox.confirm('确定要删除这个用户吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    if (confirm !== 'confirm') return

    try {
      const res = await deleteUser({ ID: row.ID })
      if (res.code === 0) {
        ElMessage.success('删除成功')
        getTableData()
      } else {
        ElMessage.error('删除失败：' + res.msg)
      }
    } catch (error) {
      ElMessage.error('删除失败')
    }
  }

  // 查看作品
  const viewAwemes = (row) => {
    selectedUser.value = row
    awemeDialogVisible.value = true
  }

  // 其他弹窗相关方法
  const showUpdateIPDialog = async (row) => {
    updateIPForm.value = {
      id: row.ID,
      nickname: row.nickname,
      currentIP: row.bindIP || '',
      newIP: '',
      availableIPs: []
    }

    // 获取可用IP列表
    try {
      const res = await getAvailableIP()
      if (res.code === 0) {
        updateIPForm.value.availableIPs = res.data
      }
    } catch (error) {
      console.error('获取可用IP失败:', error)
    }

    updateIPDialogVisible.value = true
  }

  const clearIP = () => {
    updateIPForm.value.newIP = ''
  }

  const confirmUpdateIP = async () => {
    try {
      const res = await updateUserIP({
        id: updateIPForm.value.id,
        bindIP: updateIPForm.value.newIP || null
      })

      if (res.code === 0) {
        ElMessage.success(updateIPForm.value.newIP ? '更新IP成功' : '解绑IP成功')
        updateIPDialogVisible.value = false
        getTableData()
      } else {
        ElMessage.error('操作失败：' + res.msg)
      }
    } catch (error) {
      ElMessage.error('操作失败')
    }
  }

  const showRealNameDialog = (row) => {
    realNameForm.value = {
      id: row.ID,
      nickname: row.nickname,
      realName: row.realName || ''
    }
    realNameDialogVisible.value = true
  }

  const clearRealName = () => {
    realNameForm.value.realName = ''
  }

  const confirmRealName = async () => {
    try {
      const res = await bindDevice({
        id: realNameForm.value.id,
        realName: realNameForm.value.realName || null
      })

      if (res.code === 0) {
        ElMessage.success(realNameForm.value.realName ? '实名认证成功' : '清除实名成功')
        realNameDialogVisible.value = false
        getTableData()
      } else {
        ElMessage.error('操作失败：' + res.msg)
      }
    } catch (error) {
      ElMessage.error('操作失败')
    }
  }

  const showPhoneDialog = (row) => {
    phoneForm.value = {
      id: row.ID,
      nickname: row.nickname,
      phone: row.bindPhone || '',
      selectedPhone: null
    }
    phoneDialogVisible.value = true
  }

  const phoneSearch = async (queryString, cb) => {
    if (!queryString) {
      cb([])
      return
    }

    try {
      const res = await findPhone({ phoneNumber: queryString })
      if (res.code === 0) {
        cb(res.data || [])
      } else {
        cb([])
      }
    } catch (error) {
      cb([])
    }
  }

  const phoneSelected = (item) => {
    phoneForm.value.selectedPhone = item
    phoneForm.value.phone = item.phoneNumber
  }

  const clearPhone = () => {
    phoneForm.value.phone = ''
    phoneForm.value.selectedPhone = null
  }

  const confirmPhone = async () => {
    try {
      const res = await bindDevice({
        id: phoneForm.value.id,
        bindPhone: phoneForm.value.phone || null,
        phoneOperator: phoneForm.value.selectedPhone?.operatorType || null,
        phoneUserName: phoneForm.value.selectedPhone?.realName || null
      })

      if (res.code === 0) {
        ElMessage.success(phoneForm.value.phone ? '绑定手机成功' : '解绑手机成功')
        phoneDialogVisible.value = false
        getTableData()
      } else {
        ElMessage.error('操作失败：' + res.msg)
      }
    } catch (error) {
      ElMessage.error('操作失败')
    }
  }

  const showRemarkDialog = (row) => {
    remarkForm.value = {
      id: row.ID,
      nickname: row.nickname,
      remark: row.remark || ''
    }
    remarkDialogVisible.value = true
  }

  const confirmRemark = async () => {
    try {
      const res = await updateUserRemark({
        id: remarkForm.value.id,
        remark: remarkForm.value.remark
      })

      if (res.code === 0) {
        ElMessage.success('备注更新成功')
        remarkDialogVisible.value = false
        getTableData()
      } else {
        ElMessage.error('备注更新失败：' + res.msg)
      }
    } catch (error) {
      ElMessage.error('备注更新失败')
    }
  }

  const showBindInfoDialog = async (row) => {
    currentBindInfo.value = {
      bindIP: row.bindIP || '',
      did: row.did || '',
      iid: row.iid || '',
      ID: row.ID,
      nickname: row.nickname,
      ipSort: row.ipSort || null,
      ipId: row.ipId || null,
      ipHealthStatus: row.ipHealthStatus || null
    }

    bindInfoDialogVisible.value = true

    // 如果有绑定IP，自动获取同IP用户列表
    if (row.bindIP) {
      await refreshSameIPUsers()
    }
  }

  const refreshSameIPUsers = async () => {
    if (!currentBindInfo.value.bindIP) return

    sameIPUsersLoading.value = true
    try {
      const res = await getIPBindings({ ip: currentBindInfo.value.bindIP })
      if (res.code === 0) {
        sameIPUsers.value = {
          dyUsers: res.data.dyUsers || [],
          flameUsers: res.data.flameUsers || []
        }
      }
    } catch (error) {
      console.error('获取同IP用户失败:', error)
    } finally {
      sameIPUsersLoading.value = false
    }
  }

  const checkIPHealth = async (row) => {
    if (!row.bindIP) {
      ElMessage.error('该用户未绑定IP')
      return
    }

    try {
      const res = await checkSingleIP({ ip: row.bindIP })
      if (res.code === 0) {
        row.ipHealthStatus = res.data.status
        ElMessage.success(`IP健康检查完成：${res.data.status === 'healthy' ? '健康' : '不健康'}`)
        getTableData()
      } else {
        ElMessage.error('IP健康检查失败：' + res.msg)
      }
    } catch (error) {
      ElMessage.error('IP健康检查失败')
    }
  }

  // 处理表单字段更新
  const updateFormField = (formType, field, value) => {
    switch (formType) {
      case 'updateIPForm':
        updateIPForm.value[field] = value
        break
      case 'realNameForm':
        realNameForm.value[field] = value
        break
      case 'phoneForm':
        phoneForm.value[field] = value
        break
      case 'remarkForm':
        remarkForm.value[field] = value
        break
    }
  }

  // 处理营销设定表单字段更新
  const updateMarketingFormField = (field, value) => {
    contentSettingForm.value[field] = value
  }

  // 初始化
  onMounted(() => {
    getTableData()
    fetchCategories()
  })

  onUnmounted(() => {
    if (sortableInstance.value) {
      sortableInstance.value.destroy()
    }
  })
</script>
