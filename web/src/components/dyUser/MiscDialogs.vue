<template>
  <!-- 更新IP弹窗 -->
  <el-dialog v-model="updateIPVisible" title="更新绑定IP" width="400px" center draggable>
    <el-form :model="updateIPForm" label-width="80px">
      <el-form-item label="用户">
        <span>{{ updateIPForm.nickname }}</span>
      </el-form-item>
      <el-form-item label="当前IP">
        <el-tag size="small" type="info" v-if="!updateIPForm.currentIP">未绑定</el-tag>
        <span v-else>{{ updateIPForm.currentIP }}</span>
      </el-form-item>
      <el-form-item label="新IP" prop="newIP">
        <div class="flex gap-2 items-start">
          <el-select
            v-model="updateIPForm.newIP"
            placeholder="请选择或搜索新的IP地址"
            class="flex-1"
            filterable
            clearable
          >
            <el-option v-for="ip in updateIPForm.availableIPs" :key="ip" :label="ip" :value="ip" />
          </el-select>
          <el-button type="warning" size="default" @click="$emit('clear-ip')" :disabled="!updateIPForm.newIP">
            清空
          </el-button>
        </div>
        <div class="text-gray-500 text-sm mt-1">
          {{ updateIPForm.newIP ? '请选择新的IP地址' : '清空IP后确定将解绑当前IP' }}
        </div>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="$emit('update:updateIPVisible', false)">取消</el-button>
      <el-button type="primary" @click="$emit('confirm-update-ip')">
        {{ updateIPForm.newIP ? '更新' : '解绑' }}
      </el-button>
    </template>
  </el-dialog>

  <!-- 实名弹窗 -->
  <el-dialog v-model="realNameVisible" title="实名认证" width="400px" center draggable>
    <el-form :model="realNameForm" label-width="80px">
      <el-form-item label="用户">
        <span>{{ realNameForm.nickname }}</span>
      </el-form-item>
      <el-form-item label="姓名">
        <div class="flex gap-2 items-start">
          <el-input v-model="realNameForm.realName" placeholder="请输入真实姓名，留空表示清空" class="flex-1" />
          <el-button type="warning" size="default" @click="$emit('clear-real-name')" :disabled="!realNameForm.realName">
            清空
          </el-button>
        </div>
        <div class="text-gray-500 text-sm mt-1">请输入真实姓名，或点击清空按钮清除当前实名认证</div>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="$emit('update:realNameVisible', false)">取消</el-button>
      <el-button type="primary" @click="$emit('confirm-real-name')">确定</el-button>
    </template>
  </el-dialog>

  <!-- 绑定设备弹窗 -->
  <el-dialog v-model="phoneVisible" title="绑定手机号" width="400px" center draggable>
    <el-form :model="phoneForm" label-width="80px">
      <el-form-item label="用户">
        <span>{{ phoneForm.nickname }}</span>
      </el-form-item>
      <el-form-item label="手机号">
        <div class="flex gap-2 items-start">
          <el-autocomplete
            v-model="phoneForm.phone"
            :fetch-suggestions="$emit('phone-search', $event)"
            placeholder="请输入手机号搜索，留空表示解绑"
            @select="$emit('phone-selected', $event)"
            :trigger-on-focus="true"
            popper-class="phone-suggestions"
            class="flex-1"
          >
            <template #default="{ item }">
              <div class="flex flex-col">
                <span>{{ item.phoneNumber }}</span>
                <span class="text-gray-500 text-sm">
                  {{ item.operatorType === 'mobile' ? '移动' : item.operatorType === 'unicom' ? '联通' : '电信' }} -
                  {{ item.realName || '未实名' }}
                </span>
              </div>
            </template>
          </el-autocomplete>
          <el-button type="warning" size="default" @click="$emit('clear-phone')" :disabled="!phoneForm.phone">
            清空
          </el-button>
        </div>
        <div class="text-gray-500 text-sm mt-1">
          {{ phoneForm.phone ? '请从下拉列表中选择手机号' : '清空手机号后确定将解绑当前手机号' }}
        </div>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="$emit('update:phoneVisible', false)">取消</el-button>
      <el-button type="primary" @click="$emit('confirm-phone')">
        {{ phoneForm.phone ? '绑定' : '解绑' }}
      </el-button>
    </template>
  </el-dialog>

  <!-- 添加备注弹窗 -->
  <el-dialog v-model="remarkVisible" title="账号备注" width="400px" center draggable>
    <div class="p-4">
      <el-form :model="remarkForm" label-width="80px">
        <el-form-item label="账号">
          <span>{{ remarkForm.nickname }}</span>
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="remarkForm.remark" type="textarea" :rows="3" placeholder="请输入备注信息" />
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <el-button @click="$emit('update:remarkVisible', false)">取消</el-button>
      <el-button type="primary" @click="$emit('confirm-remark')">确定</el-button>
    </template>
  </el-dialog>

  <!-- 添加绑定信息弹窗 -->
  <el-dialog v-model="bindInfoVisible" title="绑定信息" width="800px" center draggable>
    <div class="bind-info-container">
      <!-- 基础信息模块 -->
      <el-card shadow="hover" class="info-section mb-4">
        <template #header>
          <div class="section-header">
            <h4>基础信息</h4>
          </div>
        </template>
        <div class="info-content">
          <!-- 序号/WIFI信息 -->
          <div class="info-item-compact">
            <span class="info-label">序号/WIFI:</span>
            <span class="info-value">{{ bindInfo.ipSort || '未知' }}</span>
          </div>

          <!-- 代理IP信息 -->
          <div class="info-item-compact">
            <span class="info-label">代理IP:</span>
            <div class="flex items-center gap-2 flex-1">
              <span class="ip-address">{{ bindInfo.bindIP || '未绑定' }}</span>
              <el-tag
                v-if="bindInfo.bindIP && bindInfo.ipHealthStatus"
                :type="getIPHealthStatusType(bindInfo.ipHealthStatus)"
                size="small"
              >
                {{ getIPHealthStatusText(bindInfo.ipHealthStatus) }}
              </el-tag>
              <!-- 操作按钮直接放在IP旁边 -->
              <div class="flex gap-1 ml-auto">
                <el-button size="small" type="primary" @click="$emit('show-update-ip', bindInfo)">
                  {{ bindInfo.bindIP ? '更换' : '绑定' }}
                </el-button>
                <el-button
                  v-if="bindInfo.bindIP"
                  size="small"
                  type="success"
                  @click="$emit('check-ip-health', bindInfo)"
                >
                  检查健康
                </el-button>
              </div>
            </div>
          </div>

          <!-- 设备信息 -->
          <div class="info-item-compact">
            <span class="info-label">设备ID (DID):</span>
            <span class="info-value">{{ bindInfo.did || '未配置' }}</span>
          </div>
          <div class="info-item-compact">
            <span class="info-label">实例ID (IID):</span>
            <span class="info-value">{{ bindInfo.iid || '未配置' }}</span>
          </div>
        </div>
      </el-card>

      <!-- 同IP用户列表 -->
      <el-card shadow="hover" class="info-section" v-if="bindInfo.bindIP">
        <template #header>
          <div class="section-header">
            <h4>同IP用户列表</h4>
            <el-button size="small" type="primary" @click="$emit('refresh-same-ip')" :loading="sameIPLoading">
              刷新
            </el-button>
          </div>
        </template>
        <div class="same-ip-users">
          <el-tabs v-model="activeUserTab">
            <el-tab-pane label="抖音用户" name="douyin">
              <el-table :data="sameIPUsers.dyUsers" border size="small" v-if="sameIPUsers.dyUsers.length > 0">
                <el-table-column label="昵称" min-width="100">
                  <template #default="scope">
                    <div class="flex flex-col">
                      <div class="font-medium">{{ scope.row.nickname }}</div>
                      <div class="text-sm text-gray-500">{{ scope.row.bindPhone || '未绑定' }}</div>
                      <div v-if="scope.row.bindPhone" class="text-xs text-gray-400">
                        {{ scope.row.phoneOperator }}-{{ scope.row.phoneUserName || '未实名' }}
                      </div>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column prop="uniqueId" label="抖音号" min-width="120" />
                <el-table-column label="备注" min-width="150">
                  <template #default="scope">
                    <div class="whitespace-pre-line text-sm">
                      {{ scope.row.remark || '无备注' }}
                    </div>
                  </template>
                </el-table-column>
              </el-table>
              <el-empty v-else description="暂无抖音用户" :image-size="80" />
            </el-tab-pane>
            <el-tab-pane label="火山版用户" name="flame">
              <el-table :data="sameIPUsers.flameUsers" border size="small" v-if="sameIPUsers.flameUsers.length > 0">
                <el-table-column label="昵称" min-width="100">
                  <template #default="scope">
                    <div class="flex flex-col">
                      <div class="font-medium">{{ scope.row.nickname }}</div>
                      <div class="text-sm text-gray-500">{{ scope.row.bindPhone || '未绑定' }}</div>
                      <div v-if="scope.row.bindPhone" class="text-xs text-gray-400">
                        {{ scope.row.phoneOperator }}-{{ scope.row.phoneUserName || '未实名' }}
                      </div>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column prop="isCollectEnabled" label="采集状态" min-width="100">
                  <template #default="scope">
                    <el-tag :type="scope.row.isCollectEnabled ? 'success' : 'info'" size="small">
                      {{ scope.row.isCollectEnabled ? '已开启' : '未开启' }}
                    </el-tag>
                  </template>
                </el-table-column>
              </el-table>
              <el-empty v-else description="暂无火山版用户" :image-size="80" />
            </el-tab-pane>
          </el-tabs>
        </div>
      </el-card>
    </div>
  </el-dialog>

  <!-- 作品管理弹窗 -->
  <el-dialog
    v-model="awemeVisible"
    :title="`${selectedUser?.nickname || ''} - 作品管理`"
    width="95%"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    top="2vh"
    class="aweme-dialog"
  >
    <div v-if="selectedUser" class="aweme-content">
      <DyAwemeManager :initial-dy-user-ids="[selectedUser.ID]" />
    </div>
  </el-dialog>
</template>

<script setup>
import { ref } from 'vue'
import DyAwemeManager from '@/components/douyin/DyAwemeManager.vue'

defineProps({
  updateIPVisible: {
    type: Boolean,
    default: false
  },
  realNameVisible: {
    type: Boolean,
    default: false
  },
  phoneVisible: {
    type: Boolean,
    default: false
  },
  remarkVisible: {
    type: Boolean,
    default: false
  },
  bindInfoVisible: {
    type: Boolean,
    default: false
  },
  awemeVisible: {
    type: Boolean,
    default: false
  },
  updateIPForm: {
    type: Object,
    default: () => ({
      id: null,
      nickname: '',
      currentIP: '',
      newIP: '',
      availableIPs: []
    })
  },
  realNameForm: {
    type: Object,
    default: () => ({
      id: null,
      nickname: '',
      realName: ''
    })
  },
  phoneForm: {
    type: Object,
    default: () => ({
      id: null,
      nickname: '',
      phone: '',
      selectedPhone: null
    })
  },
  remarkForm: {
    type: Object,
    default: () => ({
      id: null,
      nickname: '',
      remark: ''
    })
  },
  bindInfo: {
    type: Object,
    default: () => ({
      bindIP: '',
      did: '',
      iid: '',
      ID: null,
      nickname: '',
      ipSort: null,
      ipId: null
    })
  },
  sameIPUsers: {
    type: Object,
    default: () => ({
      dyUsers: [],
      flameUsers: []
    })
  },
  sameIPLoading: {
    type: Boolean,
    default: false
  },
  selectedUser: {
    type: Object,
    default: () => ({})
  }
})

defineEmits([
  'update:updateIPVisible',
  'update:realNameVisible',
  'update:phoneVisible',
  'update:remarkVisible',
  'update:bindInfoVisible',
  'update:awemeVisible',
  'clear-ip',
  'confirm-update-ip',
  'clear-real-name',
  'confirm-real-name',
  'phone-search',
  'phone-selected',
  'clear-phone',
  'confirm-phone',
  'confirm-remark',
  'show-update-ip',
  'check-ip-health',
  'refresh-same-ip'
])

const activeUserTab = ref('douyin')

// IP健康状态相关工具方法
const getIPHealthStatusType = (status) => {
  switch (status) {
    case 'healthy':
      return 'success'
    case 'unhealthy':
      return 'danger'
    default:
      return 'info'
  }
}

const getIPHealthStatusText = (status) => {
  switch (status) {
    case 'healthy':
      return '健康'
    case 'unhealthy':
      return '不健康'
    default:
      return '未检查'
  }
}
</script>

<style scoped>
/* 作品管理弹窗样式 */
.aweme-dialog .el-dialog__body {
  padding: 10px;
  max-height: 85vh;
  overflow-y: auto;
}

.aweme-content {
  width: 100%;
  height: 100%;
}

/* 绑定信息弹窗样式 */
.bind-info-container {
  padding: 16px;
  max-height: 75vh;
  overflow-y: auto;
}

.info-section {
  border-radius: 12px;
  transition: all 0.3s ease;
}

.info-section:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.section-header h4 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.info-content {
  padding: 16px;
}

.info-item-compact {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  padding: 6px 8px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 3px solid #409eff;
}

.info-label {
  font-weight: 600;
  color: #606266;
  min-width: 120px;
  margin-right: 12px;
}

.info-value {
  color: #303133;
  font-weight: 500;
}

.ip-address {
  font-family: 'Monaco', 'Consolas', monospace;
  background: #f0f2f5;
  padding: 4px 8px;
  border-radius: 4px;
  color: #409eff;
  font-weight: 600;
}

.same-ip-users {
  padding: 16px 0;
}

.same-ip-users .el-table {
  border-radius: 8px;
  overflow: hidden;
}

.same-ip-users .el-table th {
  background: #f8f9fa;
  color: #606266;
  font-weight: 600;
}

.same-ip-users .el-empty {
  padding: 40px 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .bind-info-container {
    padding: 12px;
  }

  .info-item-compact {
    flex-direction: column;
    align-items: flex-start;
  }

  .info-label {
    min-width: auto;
    margin-bottom: 4px;
  }
}
</style>
