<template>
  <!-- 自动发布设置弹窗 -->
  <el-dialog
    v-model="visible"
    title="发布设定"
    width="800px"
    @close="$emit('close')"
    center
    draggable
  >
    <!-- 增加"日期模板" -->
    <div style="margin-top: 5px">
      <div class="flex items-center gap-2">
        <div class="section-title">日期模板：</div>
        <div class="flex flex-wrap gap-2">
          <span
            v-for="template in templateNameList"
            :key="template.templateId"
            class="px-2 py-1 bg-blue-200 rounded flex items-center gap-2"
          >
            <span @click="$emit('choose-template', template)">
              {{ template.templateName }}
            </span>
            <el-icon class="cursor-pointer mr-2" style="font-size: 14px" @click="$emit('edit-template', template)">
              <Edit />
            </el-icon>
            <el-icon class="cursor-pointer" style="font-size: 14px" @click="$emit('delete-template', template)">
              <Delete />
            </el-icon>
          </span>
        </div>
        <el-icon @click="$emit('show-template-dialog')"><CirclePlus /></el-icon>
      </div>
    </div>
    <div style="margin-top: 5px">
      <span class="section-title">
        选择日期：
        <el-link type="danger" size="mini" @click="$emit('clear-publish-data')">
          <el-icon> <Delete /> </el-icon>清空
        </el-link>
        <el-link type="primary" size="mini" @click="$emit('reset-publish-data')" class="ml-2">
          <el-icon> <Refresh /> </el-icon>默认
        </el-link>
      </span>
    </div>
    <div style="margin-top: 5px">
      <el-tabs v-model="currentDay" type="border-card">
        <el-tab-pane label="周一" :name="1"></el-tab-pane>
        <el-tab-pane label="周二" :name="2"></el-tab-pane>
        <el-tab-pane label="周三" :name="3"></el-tab-pane>
        <el-tab-pane label="周四" :name="4"></el-tab-pane>
        <el-tab-pane label="周五" :name="5"></el-tab-pane>
        <el-tab-pane label="周六" :name="6"></el-tab-pane>
        <el-tab-pane label="周日" :name="7"></el-tab-pane>
        <div class="section-title">选择时段：</div>
        <el-checkbox-group v-model="publishData[currentDay]">
          <el-checkbox :label="0">0点</el-checkbox>
          <el-checkbox :label="1">1点</el-checkbox>
          <el-checkbox :label="2">2点</el-checkbox>
          <el-checkbox :label="3">3点</el-checkbox>
          <el-checkbox :label="4">4点</el-checkbox>
          <el-checkbox :label="5">5点</el-checkbox>
          <el-checkbox :label="6">6点</el-checkbox>
          <el-checkbox :label="7">7点</el-checkbox>
          <el-checkbox :label="8">8点</el-checkbox>
          <el-checkbox :label="9">9点</el-checkbox>
          <el-checkbox :label="10">10点</el-checkbox>
          <el-checkbox :label="11">11点</el-checkbox>
          <el-checkbox :label="12">12点</el-checkbox>
          <el-checkbox :label="13">13点</el-checkbox>
          <el-checkbox :label="14">14点</el-checkbox>
          <el-checkbox :label="15">15点</el-checkbox>
          <el-checkbox :label="16">16点</el-checkbox>
          <el-checkbox :label="17">17点</el-checkbox>
          <el-checkbox :label="18">18点</el-checkbox>
          <el-checkbox :label="19">19点</el-checkbox>
          <el-checkbox :label="20">20点</el-checkbox>
          <el-checkbox :label="21">21点</el-checkbox>
          <el-checkbox :label="22">22点</el-checkbox>
          <el-checkbox :label="23">23点</el-checkbox>
        </el-checkbox-group>
      </el-tabs>
    </div>
    <div style="margin-top: 5px">
      <span class="section-title">选择类型：</span>
      <el-radio-group v-model="currentType">
        <el-radio :label="1">视频</el-radio>
        <el-radio :label="2">图文</el-radio>
      </el-radio-group>
    </div>
    <div style="margin-top: 5px">
      <span>选择类库：</span>
      <el-radio-group v-model="currentVideoCategory">
        <el-radio v-for="category in videoCategoryList" :key="category.ID" :label="category.ID">
          {{ category.name }}
        </el-radio>
      </el-radio-group>
    </div>
    <div style="margin-top: 5px">
      <span>当前状态：</span>
      <span v-if="currentUser?.autoPublishStatus === 0" class="text-gray-500">
        <el-icon>
          <CircleClose />
        </el-icon>
        未配置
      </span>
      <span v-else-if="currentUser?.autoPublishStatus === 1" class="text-green-500">
        <el-icon>
          <CircleCheck />
        </el-icon>
        已配置
      </span>
      <span v-else-if="currentUser?.autoPublishStatus === 2" class="text-red-500">
        <el-icon>
          <Warning />
        </el-icon>
        禁用
      </span>
    </div>
    <div style="margin-top: 5px">
      <span>状态设置：</span>
      <el-button-group v-if="currentUser?.autoPublishStatus === 1">
        <el-button
          size="small"
          type="warning"
          @click="$emit('change-status', 2)"
          style="margin-right: 1px"
        >
          禁用
        </el-button>
        <el-button
          size="small"
          type="danger"
          @click="$emit('change-status', 3)"
          style="margin-left: 1px"
        >
          删除
        </el-button>
      </el-button-group>
      <el-button-group v-else-if="currentUser?.autoPublishStatus === 2">
        <el-button
          size="small"
          type="success"
          @click="$emit('change-status', 1)"
          style="margin-right: 1px"
        >
          启用
        </el-button>
        <el-button
          size="small"
          type="danger"
          @click="$emit('change-status', 3)"
          style="margin-left: 1px"
        >
          删除
        </el-button>
      </el-button-group>
    </div>
    <template #footer>
      <el-button @click="$emit('close')">取消</el-button>
      <el-button type="primary" @click="$emit('confirm')">确定</el-button>
    </template>
  </el-dialog>

  <!-- 设置发布模板弹窗 -->
  <el-dialog
    v-model="templateVisible"
    title="发布时间模板"
    width="800px"
    @close="$emit('close-template')"
    center
    draggable
  >
    <!-- 增加一列输入框："模板名称" -->
    <div style="margin-top: 5px">
      <div class="flex items-center gap-2">
        <div class="section-title">模板名称：</div>
        <el-input v-model="templateName" placeholder="请输入模板名称" style="width: 200px" />
      </div>
    </div>
    <div style="margin-top: 5px">
      <span class="section-title">
        选择日期：
        <el-link type="danger" size="mini" @click="$emit('clear-template-data')">
          <el-icon> <Delete /> </el-icon>清空
        </el-link>
      </span>
    </div>
    <div style="margin-top: 5px">
      <el-tabs v-model="templateCurrentDay" type="border-card">
        <el-tab-pane label="周一" :name="1"></el-tab-pane>
        <el-tab-pane label="周二" :name="2"></el-tab-pane>
        <el-tab-pane label="周三" :name="3"></el-tab-pane>
        <el-tab-pane label="周四" :name="4"></el-tab-pane>
        <el-tab-pane label="周五" :name="5"></el-tab-pane>
        <el-tab-pane label="周六" :name="6"></el-tab-pane>
        <el-tab-pane label="周日" :name="7"></el-tab-pane>
        <div class="section-title">选择时段：</div>
        <el-checkbox-group v-model="templateData[templateCurrentDay]">
          <el-checkbox :label="0">0点</el-checkbox>
          <el-checkbox :label="1">1点</el-checkbox>
          <el-checkbox :label="2">2点</el-checkbox>
          <el-checkbox :label="3">3点</el-checkbox>
          <el-checkbox :label="4">4点</el-checkbox>
          <el-checkbox :label="5">5点</el-checkbox>
          <el-checkbox :label="6">6点</el-checkbox>
          <el-checkbox :label="7">7点</el-checkbox>
          <el-checkbox :label="8">8点</el-checkbox>
          <el-checkbox :label="9">9点</el-checkbox>
          <el-checkbox :label="10">10点</el-checkbox>
          <el-checkbox :label="11">11点</el-checkbox>
          <el-checkbox :label="12">12点</el-checkbox>
          <el-checkbox :label="13">13点</el-checkbox>
          <el-checkbox :label="14">14点</el-checkbox>
          <el-checkbox :label="15">15点</el-checkbox>
          <el-checkbox :label="16">16点</el-checkbox>
          <el-checkbox :label="17">17点</el-checkbox>
          <el-checkbox :label="18">18点</el-checkbox>
          <el-checkbox :label="19">19点</el-checkbox>
          <el-checkbox :label="20">20点</el-checkbox>
          <el-checkbox :label="21">21点</el-checkbox>
          <el-checkbox :label="22">22点</el-checkbox>
          <el-checkbox :label="23">23点</el-checkbox>
        </el-checkbox-group>
      </el-tabs>
    </div>
    <template #footer>
      <el-button @click="$emit('close-template')">取消</el-button>
      <el-button type="primary" @click="$emit('confirm-template')">确定</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { Edit, Delete, CirclePlus, Refresh, CircleClose, CircleCheck, Warning } from '@element-plus/icons-vue'

defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  templateVisible: {
    type: Boolean,
    default: false
  },
  templateNameList: {
    type: Array,
    default: () => []
  },
  currentDay: {
    type: Number,
    default: 1
  },
  currentType: {
    type: Number,
    default: 1
  },
  currentVideoCategory: {
    type: Number,
    default: null
  },
  publishData: {
    type: Object,
    default: () => ({
      1: [],
      2: [],
      3: [],
      4: [],
      5: [],
      6: [],
      7: []
    })
  },
  videoCategoryList: {
    type: Array,
    default: () => []
  },
  currentUser: {
    type: Object,
    default: () => ({})
  },
  templateName: {
    type: String,
    default: ''
  },
  templateCurrentDay: {
    type: Number,
    default: 1
  },
  templateData: {
    type: Object,
    default: () => ({
      1: [],
      2: [],
      3: [],
      4: [],
      5: [],
      6: [],
      7: []
    })
  }
})

defineEmits([
  'close',
  'choose-template',
  'edit-template',
  'delete-template',
  'show-template-dialog',
  'clear-publish-data',
  'reset-publish-data',
  'change-status',
  'confirm',
  'close-template',
  'clear-template-data',
  'confirm-template'
])
</script>

<style scoped>
.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #409eff;
  margin: 0 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid rgba(64, 158, 255, 0.2);
}
</style>
