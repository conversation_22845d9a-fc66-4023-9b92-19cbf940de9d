<template>
  <!-- 抖音号搜索弹窗 -->
  <el-dialog v-model="searchVisible" title="搜索抖音号" width="520px" center draggable>
    <div class="flex flex-col items-center gap-4">
      <el-form :model="searchForm" label-width="80px" class="w-full">
        <el-form-item label="账号类型">
          <el-radio-group v-model="searchForm.accountType">
            <el-radio :label="1">个人账号</el-radio>
            <el-radio :label="2">企业账号</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="选择分类">
          <el-tree-select
            v-model="searchForm.categoryId"
            :data="categories"
            check-strictly
            :props="defaultProps"
            :render-after-expand="false"
            style="width: 100%"
            placeholder="请选择分类"
          />
        </el-form-item>
        <el-form-item label="抖音号">
          <el-input v-model="searchForm.uniqueId" placeholder="请输入抖音号" />
        </el-form-item>
        <!-- 增加一列代理ip选项 -->
        <el-form-item label="代理IP">
          <el-input v-model="searchForm.proxy" placeholder="请获取代理IP" disabled />
          <!-- 增加两个文字按钮：1.搜索匹配 2.独立ip -->
          <el-button type="text" @click="$emit('search-proxy-fit')" style="font-size: 12px; text-decoration: underline">
            搜索匹配同手机ip
          </el-button>
          <el-button type="text" @click="$emit('get-independent-ip')" style="font-size: 12px; text-decoration: underline">
            独立IP
          </el-button>
        </el-form-item>

        <el-form-item label="匹配代理" v-if="showProxyForm">
          <!-- 在同一行增加两个元素，一个是下拉框fitProxyChannel：1-抖音号，2-代理端口。另一个是el-autocomplete组件，用于搜索匹配的代理 -->
          <el-col :span="6">
            <el-select v-model="fitProxyChannel" placeholder="请选择渠道">
              <el-option label="抖音号" value="1"></el-option>
              <el-option label="代理端口" value="2"></el-option>
              <el-option label="手机MAC地址" value="3"></el-option>
            </el-select>
          </el-col>
          <el-col :span="16">
            <el-autocomplete
              v-model="fitProxyKeyword"
              :fetch-suggestions="$emit('fit-proxy-search', $event)"
              placeholder="请根据渠道输入关键信息"
              @select="$emit('handle-fit-proxy-select', $event)"
              :trigger-on-focus="true"
              popper-class="phone-suggestions"
              class="w-full"
            >
              <template #default="{ item }">
                {{
                  Number(fitProxyChannel) === 1
                    ? item.nickname
                    : Number(fitProxyChannel) === 2
                    ? item.bindIP
                    : item.nickname || item.bindIP + ' (匹配MAC)'
                }}
              </template>
            </el-autocomplete>
          </el-col>
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <el-button type="primary" @click="$emit('auth-get-qr-code')">前往授权</el-button>
      <el-button @click="$emit('update:searchVisible', false)">关闭</el-button>
    </template>
  </el-dialog>

  <!-- 新添加授权弹窗 -->
  <el-dialog
    v-model="qrCodeVisible"
    title="获取二维码"
    width="520px"
    center
    draggable
    @open="$emit('start-polling')"
    @close="$emit('stop-polling')"
  >
    <div class="flex flex-col items-center gap-4">
      <el-form :model="authForm" label-width="80px" class="w-full">
        <el-form-item label="uniqueId">
          <el-input v-model="authForm.uniqueId" disabled />
        </el-form-item>
        <el-form-item label="选择分类" disabled>
          <el-tree-select
            v-model="authForm.categoryId"
            :data="categories"
            check-strictly
            :props="defaultProps"
            :render-after-expand="false"
            style="width: 100%"
            placeholder="请选择分类"
          />
        </el-form-item>
        <el-form-item label="did">
          <el-input v-model="authForm.did" placeholder="请输入设备ID" disabled />
        </el-form-item>
        <el-form-item label="iid">
          <el-input v-model="authForm.iid" placeholder="请输入iid" disabled />
        </el-form-item>
        <el-form-item label="代理">
          <el-input v-model="authForm.proxy" placeholder="请输入代理" disabled />
        </el-form-item>
        <el-form-item label="扫码登录">
          <!-- 显示二维码图片 -->
          <img
            v-if="authForm.qr_code_url"
            :src="`https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=${encodeURIComponent(
              authForm.qr_code_url
            )}`"
            alt="二维码"
          />
          <el-input v-model="authForm.qr_code_url" disabled v-else />
        </el-form-item>
        <div v-if="authForm.warningText" class="warning-text" style="color: orange">{{ authForm.warningText }}</div>
        <!-- 添加v-if指令来控制显示隐藏 -->
        <el-form-item label="验证码" v-if="authForm.showCaptchaInput">
          <el-input v-model="authForm.code" placeholder="请输入验证码" />
        </el-form-item>
        <el-form-item v-if="authForm.showCaptchaInput">
          <el-button @click="$emit('valid-sms')">验证</el-button>
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <el-button @click="$emit('update:qrCodeVisible', false)">关闭</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref } from 'vue'

defineProps({
  searchVisible: {
    type: Boolean,
    default: false
  },
  qrCodeVisible: {
    type: Boolean,
    default: false
  },
  categories: {
    type: Array,
    default: () => []
  },
  searchForm: {
    type: Object,
    default: () => ({
      uniqueId: null,
      accountType: null,
      categoryId: null,
      proxy: null
    })
  },
  authForm: {
    type: Object,
    default: () => ({
      categoryId: null,
      did: '',
      accountType: null,
      token: null,
      showCaptchaInput: null,
      iid: null,
      proxy: null,
      qr_code_url: null,
      code: null,
      warningText: null
    })
  },
  showProxyForm: {
    type: Boolean,
    default: false
  },
  fitProxyChannel: {
    type: String,
    default: null
  },
  fitProxyKeyword: {
    type: String,
    default: null
  }
})

defineEmits([
  'update:searchVisible',
  'update:qrCodeVisible',
  'search-proxy-fit',
  'get-independent-ip',
  'fit-proxy-search',
  'handle-fit-proxy-select',
  'auth-get-qr-code',
  'start-polling',
  'stop-polling',
  'valid-sms'
])

const defaultProps = {
  children: 'children',
  label: 'name',
  value: 'ID'
}
</script>
