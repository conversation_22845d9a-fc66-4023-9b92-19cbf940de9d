<template>
  <!-- 添加分类弹窗 -->
  <el-dialog
    v-model="visible"
    @close="handleClose"
    width="520"
    :title="(formData.ID === 0 ? '添加' : '编辑') + '分类'"
    draggable
  >
    <el-form ref="categoryForm" :rules="rules" :model="formData" label-width="80px">
      <el-form-item label="上级分类">
        <el-tree-select
          v-model="formData.pid"
          :data="categories"
          check-strictly
          :props="defaultProps"
          :render-after-expand="false"
          style="width: 100%"
        />
      </el-form-item>
      <el-form-item label="分类名称" prop="name">
        <el-input v-model.trim="formData.name" placeholder="分类名称"></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleConfirm">确定</el-button>
    </template>
  </el-dialog>

  <!-- 更新分类弹窗 -->
  <el-dialog v-model="updateVisible" title="更新分类" width="400px" center draggable>
    <el-form :model="updateFormData">
      <el-form-item label="选择分类">
        <el-select v-model="updateFormData.categoryId" placeholder="请选择分类" style="width: 100%">
          <el-option
            v-for="category in flattenCategories"
            :key="category.ID"
            :label="category.name"
            :value="category.ID"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="updateVisible = false">取消</el-button>
      <el-button type="primary" @click="handleUpdateConfirm">确定</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
  import { ref, computed } from 'vue'

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false
    },
    updateVisible: {
      type: Boolean,
      default: false
    },
    categories: {
      type: Array,
      default: () => []
    },
    formData: {
      type: Object,
      default: () => ({
        ID: 0,
        pid: 0,
        name: ''
      })
    },
    updateFormData: {
      type: Object,
      default: () => ({
        ID: null,
        categoryId: null
      })
    }
  })

  const emit = defineEmits(['update:visible', 'update:updateVisible', 'confirm', 'update-confirm', 'close'])

  const categoryForm = ref(null)

  const defaultProps = {
    children: 'children',
    label: 'name',
    value: 'ID'
  }

  const rules = ref({
    name: [
      { required: true, message: '请输入分类名称', trigger: 'blur' },
      { max: 20, message: '最多20位字符', trigger: 'blur' }
    ]
  })

  // 扁平化分类列表
  const flattenCategories = computed(() => {
    const result = []
    const flatten = (items, prefix = '') => {
      items.forEach((item) => {
        if (item.ID !== 0) {
          // 排除"全部分类"
          result.push({
            ID: item.ID,
            name: prefix + item.name
          })
        }
        if (item.children && item.children.length > 0) {
          flatten(item.children, prefix + item.name + ' / ')
        }
      })
    }
    flatten(props.categories)
    return result
  })

  const handleClose = () => {
    emit('update:visible', false)
    emit('close')
  }

  const handleConfirm = async () => {
    if (!categoryForm.value) return

    categoryForm.value.validate(async (valid) => {
      if (valid) {
        emit('confirm', props.formData)
      }
    })
  }

  const handleUpdateConfirm = () => {
    emit('update-confirm', props.updateFormData)
  }
</script>
