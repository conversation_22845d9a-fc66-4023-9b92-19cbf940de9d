<template>
  <div class="gva-table-box mt-0 mb-0 p-4">
    <warning-bar :title="'账号授权请先选择分类再扫码：抖音--我--右上角菜单--我的二维码--扫一扫'" class="mb-4" />

    <!-- 搜索和操作区域 -->
    <div class="flex flex-wrap items-center gap-3 mb-4">
      <el-button type="primary" icon="Key" @click="$emit('show-auth-dialog')" class="flex-none">
        账号授权
      </el-button>
      <div class="flex-1 flex flex-wrap gap-3">
        <el-input
          v-model="searchForm.nickname"
          placeholder="请输入用户昵称"
          class="w-full sm:w-64 md:w-56 lg:w-64"
          clearable
          @input="$emit('update:search', searchForm)"
        />
        <el-input
          v-model="searchForm.uniqueId"
          placeholder="请输入抖音号"
          class="w-full sm:w-64 md:w-56 lg:w-64"
          clearable
          @input="$emit('update:search', searchForm)"
        />
        <el-button type="primary" icon="search" @click="$emit('search')">查询</el-button>
        <el-button type="success" icon="Refresh" @click="$emit('refresh-all')">刷新</el-button>
        <el-button type="danger" icon="Refresh" @click="$emit('filter-invalid')">筛选未登录</el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'
import WarningBar from '@/components/warningBar/warningBar.vue'

const props = defineProps({
  search: {
    type: Object,
    default: () => ({
      nickname: null,
      uniqueId: null,
      categoryId: 0,
      invalidLogin: null
    })
  }
})

defineEmits(['update:search', 'search', 'refresh-all', 'filter-invalid', 'show-auth-dialog'])

const searchForm = ref({ ...props.search })

watch(() => props.search, (newVal) => {
  searchForm.value = { ...newVal }
}, { deep: true })
</script>
