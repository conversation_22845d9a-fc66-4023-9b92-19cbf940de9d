<template>
  <!-- 用户详情弹窗 -->
  <el-dialog
    :model-value="visible"
    @update:model-value="$emit('update:visible', $event)"
    title="账号详情"
    :width="dialogWidth"
    center
    draggable
  >
    <div class="user-detail-container">
      <el-row :gutter="20">
        <!-- 账号信息 -->
        <el-col :span="24" class="mb-4">
          <el-card shadow="hover" class="detail-card">
            <template #header>
              <div class="card-header">
                <h3>账号信息</h3>
              </div>
            </template>
            <el-row :gutter="20">
              <el-col :xs="24" :sm="8" :md="8">
                <div class="info-item">
                  <div class="avatar-container">
                    <el-avatar :size="80" :src="getAvatarUrl(userDetail.avatar)" />
                    <el-tag v-if="userDetail.accountType === 2" type="primary" class="account-type-tag">
                      企业号
                    </el-tag>
                  </div>
                </div>
              </el-col>
              <el-col :xs="24" :sm="8" :md="8">
                <div class="info-item">
                  <label>昵称：</label>
                  <span>{{ userDetail.nickname }}</span>
                </div>
                <div class="info-item">
                  <label>抖音号：</label>
                  <span>{{ userDetail.uniqueId }}</span>
                  <el-button link type="primary" @click="copyUniqueId(userDetail.uniqueId)" class="ml-2">
                    复制
                  </el-button>
                </div>
                <div class="info-item">
                  <label>分类：</label>
                  <span>{{ getCategoryName(userDetail.categoryId) }}</span>
                </div>
                <div class="info-item">
                  <label>录入人员：</label>
                  <span>{{ userDetail.sysUserName || '未知' }}</span>
                </div>
              </el-col>
              <el-col :xs="24" :sm="8" :md="8">
                <div class="status-item">
                  <label>登录状态：</label>
                  <el-tag :type="getStatusType(userDetail.status)">
                    {{ getStatusText(userDetail.status) }}
                  </el-tag>
                </div>
                <div class="status-item">
                  <label>私信授权：</label>
                  <el-tag :type="getTalkAuthType(userDetail.talkAuthStatus)">
                    {{ getTalkAuthText(userDetail.talkAuthStatus) }}
                  </el-tag>
                </div>
                <div class="status-item">
                  <label>发布设定：</label>
                  <el-tag :type="getPublishStatusType(userDetail.autoPublishStatus)">
                    {{ getPublishStatusText(userDetail.autoPublishStatus) }}
                  </el-tag>
                </div>
                <div class="status-item">
                  <label>营销设定：</label>
                  <el-tag :type="getTraceStatusType(userDetail.commentTraceStatus)">
                    {{ getTraceStatusText(userDetail.commentTraceStatus) }}
                  </el-tag>
                </div>
              </el-col>
            </el-row>
          </el-card>
        </el-col>

        <!-- 数据信息 -->
        <el-col :span="24" class="mb-4">
          <el-card shadow="hover" class="detail-card">
            <template #header>
              <div class="card-header">
                <h3>数据信息</h3>
              </div>
            </template>
            <div class="data-section">
              <el-row :gutter="16">
                <el-col :xs="24" :sm="8" :md="8">
                  <div class="data-item">
                    <div class="data-label">作品数</div>
                    <div class="data-value primary">{{ formatNumber(userDetail.awemeCount) }}</div>
                  </div>
                </el-col>
                <el-col :xs="24" :sm="8" :md="8">
                  <div class="data-item">
                    <div class="data-label">粉丝数</div>
                    <div class="data-value success">{{ formatNumber(userDetail.followerCount) }}</div>
                  </div>
                </el-col>
                <el-col :xs="24" :sm="8" :md="8">
                  <div class="data-item">
                    <div class="data-label">可提现</div>
                    <div class="data-value warning">¥{{ formatNumber(userDetail.withdrawableAmount / 100) }}</div>
                  </div>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </el-col>

        <!-- 销售数据 -->
        <el-col :span="24" class="mb-4">
          <el-card shadow="hover" class="detail-card">
            <template #header>
              <div class="card-header">
                <h3>销售数据</h3>
              </div>
            </template>
            <div class="sales-section">
              <el-row :gutter="16">
                <el-col :xs="24" :sm="8" :md="8">
                  <div class="sales-item">
                    <div class="sales-label">昨日销售额</div>
                    <div class="sales-value">¥{{ formatNumber(userDetail.day1TotalAmount / 100) }}</div>
                  </div>
                </el-col>
                <el-col :xs="24" :sm="8" :md="8">
                  <div class="sales-item">
                    <div class="sales-label">七日销售额</div>
                    <div class="sales-value">¥{{ formatNumber(userDetail.day7TotalAmount / 100) }}</div>
                  </div>
                </el-col>
                <el-col :xs="24" :sm="8" :md="8">
                  <div class="sales-item">
                    <div class="sales-label">总销售额</div>
                    <div class="sales-value">¥{{ formatNumber(userDetail.totalAmount / 100) }}</div>
                  </div>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </el-col>

        <!-- 技术信息 -->
        <el-col :span="24">
          <el-card shadow="hover" class="detail-card">
            <template #header>
              <div class="card-header">
                <h3>技术信息</h3>
              </div>
            </template>
            <el-row :gutter="16">
              <el-col :xs="24" :sm="12" :md="8">
                <div class="info-item">
                  <label>代理IP：</label>
                  <div class="flex flex-col gap-1">
                    <div class="flex items-center gap-2">
                      <span>{{ userDetail.bindIP || '未绑定' }}</span>
                      <el-tag
                        v-if="userDetail.bindIP && userDetail.ipHealthStatus"
                        :type="getIPHealthStatusType(userDetail.ipHealthStatus)"
                        size="small"
                      >
                        {{ getIPHealthStatusText(userDetail.ipHealthStatus) }}
                      </el-tag>
                    </div>
                    <el-button
                      v-if="userDetail.bindIP"
                      size="small"
                      type="success"
                      link
                      @click="$emit('check-ip-health', userDetail)"
                    >
                      检查健康状态
                    </el-button>
                  </div>
                </div>
              </el-col>
              <el-col :xs="24" :sm="12" :md="8">
                <div class="info-item">
                  <label>设备ID：</label>
                  <span>{{ userDetail.did || '未配置' }}</span>
                </div>
              </el-col>
              <el-col :xs="24" :sm="12" :md="8">
                <div class="info-item">
                  <label>实例ID：</label>
                  <span>{{ userDetail.iid || '未配置' }}</span>
                </div>
              </el-col>
              <el-col :xs="24" :sm="12" :md="8">
                <div class="info-item">
                  <label>实名认证：</label>
                  <span>{{ userDetail.realName || '未实名' }}</span>
                </div>
              </el-col>
              <el-col :xs="24" :sm="12" :md="8">
                <div class="info-item">
                  <label>绑定手机：</label>
                  <span>{{ userDetail.bindPhone || '未绑定' }}</span>
                </div>
              </el-col>
              <el-col :xs="24" :sm="12" :md="8">
                <div class="info-item">
                  <label>手机实名：</label>
                  <span>{{ userDetail.phoneUserName || '未实名' }}</span>
                </div>
              </el-col>
            </el-row>
            <div class="info-item mt-3" v-if="userDetail.remark">
              <label>备注：</label>
              <div class="remark-content">{{ userDetail.remark }}</div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </el-dialog>
</template>

<script setup>
  import { computed } from 'vue'
  import { ElMessage } from 'element-plus'

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false
    },
    userDetail: {
      type: Object,
      default: () => ({})
    },
    categories: {
      type: Array,
      default: () => []
    }
  })

  defineEmits(['update:visible', 'check-ip-health'])

  // 响应式对话框宽度
  const dialogWidth = computed(() => {
    if (typeof window !== 'undefined') {
      return window.innerWidth <= 768 ? '95%' : '800px'
    }
    return '800px'
  })

  // 工具方法
  const formatNumber = (num) => {
    if (num >= 10000) {
      return (num / 10000).toFixed(1) + 'w'
    }
    return num
  }

  const getAvatarUrl = (avatarJson) => {
    try {
      if (!avatarJson) return ''
      const avatarData = JSON.parse(avatarJson)
      return avatarData.url_list?.[0] || ''
    } catch (err) {
      console.error('解析头像JSON失败:', err)
      return ''
    }
  }

  const getCategoryName = (categoryId) => {
    const findCategory = (list) => {
      for (const item of list) {
        if (item.ID === categoryId) {
          return item.name
        }
        if (item.children && item.children.length > 0) {
          const found = findCategory(item.children)
          if (found) return found
        }
      }
      return null
    }
    const name = findCategory(props.categories)
    if (name) {
      return name
    }
    return `已删除的分类(${categoryId})`
  }

  // 状态类型和文本获取方法
  const getStatusType = (status) => {
    switch (status) {
      case 1:
        return 'success'
      case 2:
        return 'info'
      case 3:
        return 'warning'
      default:
        return 'danger'
    }
  }

  const getStatusText = (status) => {
    switch (status) {
      case 1:
        return '正常'
      case 2:
        return '失效'
      case 3:
        return '超时重试中'
      default:
        return '未登录'
    }
  }

  const getTalkAuthType = (status) => {
    switch (status) {
      case 1:
        return 'success'
      case 2:
        return 'warning'
      case 3:
        return 'danger'
      default:
        return 'info'
    }
  }

  const getTalkAuthText = (status) => {
    switch (status) {
      case 1:
        return '正常'
      case 2:
        return '已失效'
      case 3:
        return '禁用'
      default:
        return '未授权'
    }
  }

  const getPublishStatusType = (status) => {
    switch (status) {
      case 1:
        return 'success'
      case 2:
        return 'danger'
      default:
        return 'info'
    }
  }

  const getPublishStatusText = (status) => {
    switch (status) {
      case 1:
        return '已设置'
      case 2:
        return '禁用'
      default:
        return '未配置'
    }
  }

  const getTraceStatusType = (status) => {
    switch (status) {
      case 1:
        return 'success'
      case 2:
        return 'danger'
      default:
        return 'info'
    }
  }

  const getTraceStatusText = (status) => {
    switch (status) {
      case 1:
        return '已配置'
      case 2:
        return '禁用'
      default:
        return '未配置'
    }
  }

  // IP健康状态相关工具方法
  const getIPHealthStatusType = (status) => {
    switch (status) {
      case 'healthy':
        return 'success'
      case 'unhealthy':
        return 'danger'
      default:
        return 'info'
    }
  }

  const getIPHealthStatusText = (status) => {
    switch (status) {
      case 'healthy':
        return '健康'
      case 'unhealthy':
        return '不健康'
      default:
        return '未检查'
    }
  }

  // 复制抖音号功能
  const copyUniqueId = async (uniqueId) => {
    try {
      await navigator.clipboard.writeText(uniqueId)
      ElMessage.success('抖音号已复制到剪贴板')
    } catch {
      // 如果剪贴板API不可用，使用传统方法
      const textArea = document.createElement('textarea')
      textArea.value = uniqueId
      document.body.appendChild(textArea)
      textArea.select()
      try {
        document.execCommand('copy')
        ElMessage.success('抖音号已复制到剪贴板')
      } catch {
        ElMessage.error('复制失败，请手动复制')
      }
      document.body.removeChild(textArea)
    }
  }
</script>

<style scoped>
  /* 用户详情弹窗样式 */
  .user-detail-container {
    max-height: 70vh;
    overflow-y: auto;
  }

  .detail-card {
    margin-bottom: 16px;
  }

  .card-header h3 {
    margin: 0;
    color: #303133;
    font-size: 16px;
    font-weight: 600;
  }

  .avatar-container {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
  }

  .account-type-tag {
    position: absolute;
    top: -5px;
    right: -5px;
    font-size: 10px;
  }

  .info-item {
    margin-bottom: 12px;
  }

  .info-item label {
    font-weight: 600;
    color: #606266;
    margin-right: 8px;
  }

  .status-item {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
  }

  .status-item label {
    font-weight: 600;
    color: #606266;
    min-width: 80px;
  }

  .data-section {
    padding: 16px;
    background: rgba(64, 158, 255, 0.02);
    border-radius: 12px;
    border: 1px solid rgba(64, 158, 255, 0.1);
  }

  .data-item {
    text-align: center;
    padding: 20px;
    border-radius: 12px;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border: 1px solid #e2e8f0;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    margin-bottom: 16px;
  }

  .data-item:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }

  .data-label {
    font-size: 12px;
    color: #909399;
    margin-bottom: 8px;
  }

  .data-value {
    font-size: 18px;
    font-weight: 600;
  }

  .data-value.primary {
    color: #409eff;
  }

  .data-value.success {
    color: #67c23a;
  }

  .data-value.warning {
    color: #e6a23c;
  }

  .sales-section {
    padding: 16px;
  }

  .sales-item {
    text-align: center;
    padding: 24px;
    border-radius: 16px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 8px 30px rgba(102, 126, 234, 0.3);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    margin-bottom: 16px;
  }

  .sales-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    pointer-events: none;
  }

  .sales-item:hover {
    transform: translateY(-6px) scale(1.02);
    box-shadow: 0 12px 40px rgba(102, 126, 234, 0.4);
  }

  .sales-label {
    font-size: 14px;
    opacity: 0.9;
    margin-bottom: 12px;
    font-weight: 500;
    position: relative;
    z-index: 1;
  }

  .sales-value {
    font-size: 22px;
    font-weight: 700;
    position: relative;
    z-index: 1;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }

  .remark-content {
    background: #f5f7fa;
    padding: 12px;
    border-radius: 6px;
    border-left: 4px solid #409eff;
    white-space: pre-wrap;
    margin-top: 8px;
  }

  /* 移动端适配 */
  @media (max-width: 768px) {
    .user-detail-container {
      padding: 0;
      max-height: 80vh;
    }

    .avatar-container {
      margin-bottom: 16px;
      text-align: center;
    }

    .info-item {
      margin-bottom: 8px;
      display: flex;
      flex-direction: column;
      align-items: flex-start;
    }

    .info-item label {
      font-size: 12px;
      margin-bottom: 4px;
    }

    .status-item {
      flex-direction: column;
      align-items: flex-start;
      gap: 4px;
    }

    .status-item label {
      min-width: auto;
      font-size: 12px;
    }

    .data-item {
      padding: 12px;
    }

    .sales-item {
      padding: 16px;
    }

    .sales-value,
    .data-value {
      font-size: 16px;
    }

    .remark-content {
      padding: 8px;
      font-size: 14px;
    }

    .detail-card {
      margin-bottom: 12px;
    }

    .card-header h3 {
      font-size: 14px;
    }
  }
</style>
