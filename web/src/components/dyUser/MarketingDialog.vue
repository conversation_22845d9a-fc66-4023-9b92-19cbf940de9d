<template>
  <!--营销设定弹窗-->
  <el-dialog
    :model-value="visible"
    @update:model-value="$emit('update:visible', $event)"
    title="营销设定"
    width="600px"
    center
    draggable
  >
    <el-form :model="formData" label-width="120px">
      <!-- 增加文字说明："评论设定：命中关键词->回复->关注->私信" -->
      <div class="bg-emerald-100 text-emerald-700 p-3 rounded mb-4">追踪一级评论：命中关键词->回复->关注->发私信</div>
      <!-- 评论设定部分 -->
      <el-form-item label="评论模板">
        <el-input
          :model-value="formData.commentTemplates"
          @update:model-value="$emit('update-form-field', 'commentTemplates', $event)"
          type="textarea"
          :rows="5"
          placeholder="请输入评论模板（一个换行表示一条）"
          style="width: calc(100% - 80px)"
        />
      </el-form-item>
      <el-form-item label="关键词">
        <el-input
          :model-value="formData.commentKeyword"
          @update:model-value="$emit('update-form-field', 'commentKeyword', $event)"
          placeholder="多个关键词用逗号分隔"
          style="width: calc(100% - 80px)"
        />
      </el-form-item>

      <el-divider />

      <!-- 新增回复设定部分 -->
      <div class="bg-emerald-100 text-emerald-700 p-3 rounded mb-4">私信授权：关注->发私信</div>
      <el-form-item label="第一条私信">
        <el-button type="primary" @click="$emit('get-promotion-link', formData)">获取推广链接</el-button>
        <span class="ml-2 text-sm" :class="formData.promotionLink ? 'text-green-500' : 'text-red-500'">
          {{ formData.promotionLink ? '已获取' : '未获取' }}
        </span>
      </el-form-item>
      <el-form-item label="第二条私信">
        <el-input
          :model-value="formData.replyTemplates"
          @update:model-value="$emit('update-form-field', 'replyTemplates', $event)"
          type="textarea"
          :rows="5"
          placeholder="请输入自动回复模板（一个换行表示一条）"
          style="width: calc(100% - 80px)"
        />
      </el-form-item>
      <el-form-item label="营销授权状态">
        <span v-if="formData.talkAuthStatus === 0" class="text-gray-500">
          <el-icon>
            <CircleClose />
          </el-icon>
          未授权
        </span>
        <span v-else-if="formData.talkAuthStatus === 1" class="text-green-500">
          <el-icon>
            <CircleCheck />
          </el-icon>
          正常
        </span>
        <span v-else-if="formData.talkAuthStatus === 2" class="text-red-500">
          <el-icon>
            <Warning />
          </el-icon>
          授权失效
        </span>
        <span v-else-if="formData.talkAuthStatus === 3" class="text-gray-500">
          <el-icon>
            <Close />
          </el-icon>
          禁用
        </span>
        <span v-else class="text-gray-500">
          <el-icon>
            <QuestionFilled />
          </el-icon>
          未知状态
        </span>
      </el-form-item>
      <el-form-item label="授权操作">
        <div class="flex items-center gap-4">
          <div class="flex gap-2">
            <el-button
              v-if="formData.talkAuthStatus !== 3"
              type="primary"
              size="small"
              @click="$emit('handle-talk-auth', formData, 1)"
              :loading="openBrowserLoading"
            >
              前往授权
            </el-button>
            <el-button
              v-if="formData.biteBrowserId !== ''"
              type="warning"
              size="small"
              @click="$emit('clear-talk-auth', formData)"
            >
              清空授权
            </el-button>
            <el-button
              v-if="formData.talkAuthStatus === 1"
              type="danger"
              size="small"
              @click="$emit('handle-talk-auth', formData, 3)"
            >
              禁用
            </el-button>
            <el-button
              v-if="formData.talkAuthStatus === 3"
              type="success"
              size="small"
              @click="$emit('handle-talk-auth', formData, 1)"
              :loading="openBrowserLoading"
            >
              启用
            </el-button>
          </div>
        </div>
      </el-form-item>
      <!-- 增加一列按钮"在比特浏览器登录抖音成功后，请按下此按钮"，当isOpenBrowser为true时显示 -->
      <el-form-item v-if="isOpenBrowser" label="">
        <el-button type="primary" size="small" @click="$emit('confirm-login-bite')">
          在比特浏览器成功登录抖音后，请按下此按钮
        </el-button>
      </el-form-item>

      <el-divider />
      <div>
        <span>营销状态：</span>
        <span v-if="formData?.commentTraceStatus === 0" class="text-gray-500">
          <el-icon>
            <CircleClose />
          </el-icon>
          未配置
        </span>
        <span v-else-if="formData?.commentTraceStatus === 1" class="text-green-500">
          <el-icon>
            <CircleCheck />
          </el-icon>
          已配置
        </span>
        <span v-else-if="formData?.commentTraceStatus === 2" class="text-red-500">
          <el-icon>
            <Warning />
          </el-icon>
          禁用
        </span>
      </div>
      <div>
        <span>状态设置：</span>
        <el-button-group v-if="formData.commentTraceStatus === 1">
          <el-button size="small" type="warning" @click="$emit('update-track-status', 2)" style="margin-right: 1px">
            禁用
          </el-button>
        </el-button-group>
        <el-button-group v-else-if="formData?.commentTraceStatus === 2">
          <el-button size="small" type="success" @click="$emit('update-track-status', 1)" style="margin-right: 1px">
            启用
          </el-button>
        </el-button-group>
      </div>
    </el-form>
    <template #footer>
      <el-button @click="$emit('close')">取消</el-button>
      <el-button type="primary" @click="$emit('save')">保存</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
  import { CircleClose, CircleCheck, Warning, Close, QuestionFilled } from '@element-plus/icons-vue'

  defineProps({
    visible: {
      type: Boolean,
      default: false
    },
    formData: {
      type: Object,
      default: () => ({
        ID: null,
        commentTemplates: '',
        commentTraceStatus: 0,
        commentKeyword: '',
        talkAuthStatus: 0, // 私信授权状态
        promotionLink: '', // 推广链接
        biteBrowserId: '', // 比特浏览器ID
        proxy: '', // 代理
        uniqueId: '', // 抖音号
        replyTemplates: '' // 回复模板
      })
    },
    openBrowserLoading: {
      type: Boolean,
      default: false
    },
    isOpenBrowser: {
      type: Boolean,
      default: false
    }
  })

  defineEmits([
    'update:visible',
    'close',
    'save',
    'get-promotion-link',
    'handle-talk-auth',
    'clear-talk-auth',
    'confirm-login-bite',
    'update-track-status',
    'update-form-field'
  ])
</script>
