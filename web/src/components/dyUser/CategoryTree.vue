<template>
  <div
    class="w-full md:w-72 lg:w-80 bg-white text-slate-700 dark:text-slate-400 dark:bg-slate-900 rounded-lg shadow-sm p-4"
  >
    <div class="flex items-center justify-between mb-4">
      <h3 class="text-lg font-medium">账号分类</h3>
    </div>
    <el-scrollbar style="height: calc(100vh - 320px)">
      <el-tree
        :data="categories"
        node-key="id"
        :props="defaultProps"
        @node-click="handleNodeClick"
        default-expand-all
        highlight-current
      >
        <template #default="{ data }">
          <div class="flex items-center justify-between w-full py-1">
            <div class="truncate" :class="selectedCategoryId === data.ID ? 'text-blue-500 font-bold' : ''">
              {{ data.name }}
            </div>
            <el-dropdown>
              <el-icon class="ml-3 cursor-pointer" v-if="data.ID > 0">
                <MoreFilled />
              </el-icon>
              <el-icon class="ml-3 cursor-pointer" v-else>
                <Plus />
              </el-icon>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="$emit('add-category', data)">添加分类</el-dropdown-item>
                  <el-dropdown-item @click="$emit('edit-category', data)" v-if="data.ID > 0">编辑分类</el-dropdown-item>
                  <el-dropdown-item @click="$emit('delete-category', data.ID)" v-if="data.ID > 0"
                    >删除分类</el-dropdown-item
                  >
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </template>
      </el-tree>
    </el-scrollbar>
  </div>
</template>

<script setup>
  import { MoreFilled, Plus } from '@element-plus/icons-vue'

  defineProps({
    categories: {
      type: Array,
      default: () => []
    },
    selectedCategoryId: {
      type: Number,
      default: 0
    }
  })

  const defaultProps = {
    children: 'children',
    label: 'name',
    value: 'ID'
  }

  const emit = defineEmits(['node-click', 'add-category', 'edit-category', 'delete-category'])

  const handleNodeClick = (node) => {
    emit('node-click', node)
  }
</script>
